# Minesweep 游戏 WebSocket API 文档

## 概述

本文档描述了 Minesweep 游戏的 WebSocket API 接口。所有消息都通过 WebSocket 连接进行通信。

## 消息格式

### 请求消息格式
```json
{
  "msgId": "消息类型",
  "data": {
    // 具体参数
  }
}
```

### 响应消息格式
```json
{
  "msgId": "消息类型",
  "code": 0,
  "msg": "success",
  "data": {
    // 响应数据
  }
}
```

## API 接口列表

### 1. 登录接口

**WebSocket消息类型**：`"Login"`

玩家登录游戏。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 否 | String | app的语聊房Id，非语聊房赋值为空 |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userInfo | - | 是 | Object | 用户信息 |
| userInfo.userId | - | 是 | String | 玩家ID |
| userInfo.nickname | - | 是 | String | 昵称 |
| userInfo.avatar | - | 是 | String | 头像 |
| userInfo.coin | - | 是 | Number | 当前金币 |
| userInfo.serverTime | - | 是 | Number | 服务器的秒级时间戳 |
| roomId | - | 是 | Number | 游戏中房间ID（>0表示断线重连） |
| inviteCode | - | 是 | Number | 邀请码（>0表示已创建房间） |
| hideCoin | - | 是 | Boolean | 是否隐藏金币 |
| roomConfigs | - | 是 | Array | 房间配置列表 |

### 2. 请求匹配

**WebSocket消息类型**：`"PairRequest"`

玩家请求匹配其他玩家开始游戏。地图类型由服务端随机生成。

**匹配流程说明**：
1. 客户端发送匹配请求
2. 服务端立即响应（表示已加入匹配队列）
3. 匹配成功后，服务端发送 `GameStart` 通知（包含完整游戏信息）

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| playerNum | - | 是 | Number | 玩家人数（2-4） |
| fee | - | 是 | Number | 房间费用 |

#### 响应参数

成功时返回空对象 `{}`（表示已成功加入匹配队列）

**注意**：匹配成功后会收到 `GameStart` 通知消息，包含房间ID、地图类型等完整游戏信息。

### 3. 取消匹配

**WebSocket消息类型**：`"CancelPair"`

玩家取消匹配请求。

#### 请求参数

无参数

#### 响应参数

成功时返回空对象 `{}`

### 4. 创建邀请

**WebSocket消息类型**：`"CreateInvite"`

创建邀请房间。地图类型由服务端随机生成。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| playerNum | - | 是 | Number | 玩家人数（2-4） |
| fee | - | 是 | Number | 房间费用 |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| inviteCode | - | 是 | Number | 邀请码 |
| inviteInfo | - | 是 | Object | 邀请信息，参考InviteInfo结构 |

### 5. 接受邀请

**WebSocket消息类型**：`"AcceptInvite"`

接受邀请加入房间。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| inviteCode | - | 是 | Number | 邀请码 |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | - | 是 | String | 玩家ID |
| inviteInfo | - | 是 | Object | 邀请信息，参考InviteInfo结构 |

### 6. 邀请准备

**WebSocket消息类型**：`"InviteReady"`

邀请房间中玩家改变准备状态。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| ready | - | 是 | Boolean | 是否准备 |

#### 响应参数

成功时返回空对象 `{}`

### 7. 进入房间

**WebSocket消息类型**：`"EnterRoom"`

玩家进入游戏房间。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| lastRoomID | - | 否 | Number | 上一次房间ID |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | Number | 房间ID |
| roomType | - | 是 | Number | 房间类型（1-普通场，2-私人场，3-语聊房） |
| playerNum | - | 是 | Number | 玩家人数 |
| mapType | - | 是 | Number | 地图类型（0-方格地图，1-六边形地图） |
| fee | - | 是 | Number | 房间费 |
| users | - | 是 | Array | 房间内所有玩家，参考RoomUser结构 |
| gameStatus | - | 是 | Number | 房间游戏状态 |
| countDown | - | 是 | Number | 房间游戏状态倒计时 |
| firstMoves | - | 是 | Array | 先手列表 |
| blockList | - | 是 | Array | 地图，参考Block结构 |
| tokenInfo | - | 是 | Object | 当前Token信息 |
| blockUserList | - | 是 | Object | 块上的用户列表 |

### 8. 坐下

**WebSocket消息类型**：`"SitDown"`

玩家请求坐下。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| pos | - | 是 | Number | 座位号 |

#### 响应参数

成功时返回空对象 `{}`

### 9. 准备

**WebSocket消息类型**：`"Ready"`

玩家请求准备。

#### 请求参数

无参数

#### 响应参数

成功时返回空对象 `{}`

### 12. 点击方块

**WebSocket消息类型**：`"ClickBlock"`

玩家在扫雷游戏中点击方块进行操作。

**使用场景**：
- 在扫雷游戏中使用（支持方格地图mapType=0和六边形地图mapType=1）
- 在回合时间内（25秒）可以操作
- 每回合每个玩家只能操作一次，后续操作会覆盖前面的操作

#### 请求参数

**坐标参数根据地图类型而不同**：

##### 方格地图（mapType=0）
| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | - | 是 | Number | 方块x坐标（0-7，从左到右） |
| y | - | 是 | Number | 方块y坐标（0-7，从下到上，左下角为(0,0)） |
| action | - | 是 | Number | 操作类型：1=挖掘方块，2=标记/取消标记地雷 |

##### 六边形地图（mapType=1）
| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | - | 是 | Number | 六边形q坐标（轴向坐标系统） |
| y | - | 是 | Number | 六边形r坐标（轴向坐标系统） |
| action | - | 是 | Number | 操作类型：1=挖掘方块，2=标记/取消标记地雷 |

**注意**：六边形地图中，x参数对应q坐标，y参数对应r坐标。

#### 请求示例

##### 方格地图请求示例
```json
{
  "msgId": "ClickBlock",
  "data": {
    "x": 3,
    "y": 2,
    "action": 1
  }
}
```

##### 六边形地图请求示例
```json
{
  "msgId": "ClickBlock",
  "data": {
    "x": 1,
    "y": -1,
    "action": 1
  }
}
```

#### 响应参数

成功时返回空对象 `{}`

**操作类型说明**：
- `action=1`：挖掘方块，揭示方块内容（数字或地雷）
- `action=2`：标记/取消标记地雷，切换方块的标记状态

**注意**：
- 前20秒操作不会立即广播给其他玩家
- 第20秒后会统一显示所有玩家的操作
- 多个玩家可以选择同一个方块
- 标记操作（action=2）会自动切换标记状态：未标记→标记，标记→取消标记

### 13. 离开房间

**WebSocket消息类型**：`"LeaveRoom"`

玩家主动离开房间。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| isConfirmLeave | - | 是 | Boolean | 确认离开房间 |

#### 响应参数

成功时返回空对象 `{}`

## 语聊房相关接口

### 17. 进入语聊房

**WebSocket消息类型**：`"EnterVoiceRoom"`

进入语聊房。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| platRoomID | - | 是 | String | 语聊房Id |
| playerNum | - | 是 | Number | 玩家人数 |
| fee | - | 是 | Number | 房间费 |
| users | - | 是 | Array | 玩家列表，参考VoiceRoomUser结构 |
| roomId | - | 是 | Number | 与之相关的游戏房间Id |
| kickOut | - | 是 | Boolean | 是否允许踢除玩家 |

### 18. 语聊房坐下

**WebSocket消息类型**：`"VoiceUserSit"`

在语聊房中坐下。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| pos | - | 是 | Number | 请求坐下的游戏位置 |

#### 响应参数

成功时返回空对象 `{}`

### 19. 语聊房站起

**WebSocket消息类型**：`"VoiceUserStandUp"`

在语聊房中站起。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

成功时返回空对象 `{}`

### 20. 修改语聊房配置

**WebSocket消息类型**：`"ChangeVoiceCfg"`

修改语聊房配置。地图类型由服务端随机生成。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| playerNum | - | 是 | Number | 玩家人数 |
| fee | - | 是 | Number | 房间费 |

#### 响应参数

成功时返回空对象 `{}`

### 21. 语聊房准备

**WebSocket消息类型**：`"VoiceUserReady"`

语聊房玩家准备。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| ready | - | 是 | Boolean | 是否准备 |

#### 响应参数

成功时返回空对象 `{}`

### 22. 语聊房开始游戏

**WebSocket消息类型**：`"VoiceStartGame"`

语聊房开始游戏。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |

#### 响应参数

成功时返回空对象 `{}`

### 23. 语聊房踢人

**WebSocket消息类型**：`"VoiceKickOut"`

语聊房踢除玩家。

#### 请求参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roomId | - | 是 | String | app的语聊房Id |
| userId | - | 是 | String | 玩家ID |

#### 响应参数

成功时返回空对象 `{}`

## 通知类消息

### 24. 匹配结果通知

**WebSocket消息类型**：`"GameStart"`

服务端通知客户端匹配到了其他玩家并开始游戏。此通知在 `PairRequest` 匹配成功后自动发送，包含完整的游戏房间信息和随机生成的地图类型。

**地图类型说明**：

- mapType=0：方形地图，响应中包含 `mapConfig` 字段
- mapType=1：六边形地图，响应中包含 `validHexCoords` 字段

**扫雷游戏流程**：

1. 发送 `GameStart` 通知
2. 前端播放2秒开场动画
3. 2秒后发送 `NoticeRoundStart` 通知，正式开始游戏

#### 通知参数

| 参数名         | 参数值     | 是否必填 | 参数类型 | 描述说明                                                    |
|--------|--------|----------|----------|----------|
| roomId         | 12345      | 是       | Number   | 房间ID                                                      |
| roomType       | 1          | 是       | Number   | 房间类型（1-普通场，2-私人场，3-语聊房）                    |
| playerNum      | 2-4        | 是       | Number   | 玩家人数                                                    |
| mapType        | 0/1        | 是       | Number   | 地图类型（0-方形地图，1-六边形地图）**由服务端随机生成**    |
| fee            | 0/500/1000 | 是       | Number   | 房间费                                                      |
| users          | -          | 是       | Array    | 匹配到的所有玩家，参考RoomUser结构                          |
| gameStatus     | 1          | 是       | Number   | 游戏状态                                                    |
| countDown      | 0          | 是       | Number   | 游戏状态倒计时                                              |
| validHexCoords | -          | 否       | Array    | 有效的六边形坐标列表（仅mapType=1时返回），参考HexCoord结构 |
| mapConfig      | -          | 否       | Object   | 地图配置信息（仅mapType=0时返回），参考MapConfig结构        |

#### MapConfig结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| width | 8 | 是 | Number | 地图宽度（8） |
| height | 8 | 是 | Number | 地图高度（8） |
| mineCount | 13 | 是 | Number | 地雷总数（13） |

#### HexCoord 结构（六边形地图专用）

| 参数名 | 参数值 | 参数类型 | 是否必填 | 描述说明          |
| ------ | ------ | -------- | -------- | ----------------- |
| q      | 0      | Number   | 是       | 六边形坐标系q坐标 |
| r      | 0      | Number   | 是       | 六边形坐标系r坐标 |



#### 响应示例

**方形地图响应示例（mapType=0）**：

```json
{
  "msgId": "GameStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roomId": 12345,
    "roomType": 1,
    "playerNum": 2,
    "mapType": 0,
    "fee": 100,
    "users": [
      {
        "userId": "player_001",
        "nickName": "玩家1",
        "avatar": "avatar1.jpg",
        "pos": 1,
        "coin": 1000,
        "status": 3,
        "skinChessId": 1001
      }
    ],
    "gameStatus": 1,
    "countDown": 0,
    "mapConfig": {
      "width": 8,
      "height": 8,
      "mineCount": 13
    }
  }
}
```

**六边形地图响应示例（mapType=1）**：

```json
{
  "msgId": "GameStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roomId": 12346,
    "roomType": 1,
    "playerNum": 2,
    "mapType": 1,
    "fee": 100,
    "users": [
      {
        "userId": "player_001",
        "nickName": "玩家1",
        "avatar": "avatar1.jpg",
        "pos": 1,
        "coin": 1000,
        "status": 3,
        "minesFound": 0,
        "blocksRevealed": 0,
        "gameScore": 0,
        "isAlive": true
      }
    ],
    "gameStatus": 1,
    "countDown": 300,
    "validHexCoords": [
      { "q": 0, "r": 0 },
      { "q": 1, "r": 0 },
      { "q": 1, "r": -1 },
      { "q": 0, "r": -1 },
      { "q": -1, "r": 0 },
      { "q": -1, "r": 1 },
      { "q": 0, "r": 1 }
    ]
  }
}
```

### 25. 扫雷回合开始通知

**WebSocket消息类型**：`"NoticeRoundStart"`

服务端通知客户端扫雷游戏回合开始。此通知在游戏开始2秒延迟后自动发送，标志着扫雷游戏正式开始。

**发送时机**：
- 在 `GameStart` 发送2秒后自动发送
- 后端同时开始25秒回合倒计时
- 前端收到此消息后立即进入游戏状态

**回合时间机制**：
- **前20秒**：玩家可以进行挖掘和标记操作
- **后5秒**：展示阶段，显示所有玩家操作但不允许新操作
- **第25秒**：回合正式结束，发送NoticeRoundEnd通知

**AI托管机制**：
- **触发时机**：第20秒时，为未选择格子的玩家执行AI托管
- **托管行为**：系统随机选择一格进行操作（70%概率挖掘，30%概率标记）
- **奖励规则**：AI托管状态下不享受首选玩家+1分奖励
- **优先级**：优先选择未被挖掘的格子，如无可用格子则随机选择
- **展示效果**：AI托管后立即在NoticeActionDisplay中显示所有玩家操作

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roundNumber | 1 | 是 | Number | 回合编号（从1开始） |
| countDown | 25 | 是 | Number | 回合倒计时（25秒） |
| gameStatus | 0 | 是 | Number | 游戏状态（0-扫雷进行中） |
| remainingMines | 13 | 是 | Number | 剩余地雷数量（总地雷数减去已正确标记的地雷数） |

#### 响应示例

```json
{
  "msgId": "NoticeRoundStart",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "countDown": 25,
    "gameStatus": 0,
    "remainingMines": 13
  }
}
```

### 26. 扫雷操作展示通知

**WebSocket消息类型**：`"NoticeActionDisplay"`

服务端通知客户端进入操作展示阶段。此通知在第20秒时发送，包含完整的游戏数据供前端展示5秒动画效果。

**发送时机**：
- 当倒计时从6变为5时自动发送
- 或所有玩家操作完毕时立即发送
- 先为未操作玩家执行AI托管，再发送此通知
- 包含完整的操作结果、地图数据和得分信息
- 进入5秒展示阶段，不允许新操作

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roundNumber | 1 | 是 | Number | 当前回合编号 |
| gameStatus | 0 | 是 | Number | 游戏状态（0-扫雷进行中） |
| countDown | 5 | 是 | Number | 剩余倒计时（5秒展示阶段） |
| playerActions | - | 是 | Array | 玩家操作结果列表，参考PlayerAction结构 |
| floodFillResults | - | 否 | Array | 连锁展开结果列表，参考FloodFillData结构 |
| playerTotalScores | - | 是 | Object | 玩家累计总得分对象，属性名为userID，属性值为分数(Number) |
| remainingMines | 8 | 是 | Number | 剩余地雷数量（总地雷数减去已正确标记的地雷数） |
| message | "展示阶段：显示所有玩家操作和得分" | 是 | String | 提示信息 |

#### PlayerAction结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | "1" | 是 | String | 玩家ID |
| x | 3 | 是 | Number | 操作坐标x |
| y | 2 | 是 | Number | 操作坐标y |
| action | 1 | 是 | Number | 操作类型（1-挖掘，2-标记） |
| score | 10 | 是 | Number | 本次操作得分 |
| isFirstChoice | true | 是 | Boolean | 是否为首选玩家 |
| result | 2/"mine"/"correct_mark"/"wrong_mark" | 是 | Number/String | 操作结果（挖掘：数字或"mine"；标记："correct_mark"或"wrong_mark"） |

#### FloodFillData结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| triggerUserId | "1" | 是 | String | 触发连锁展开的玩家ID |
| triggerX | 3 | 是 | Number | 触发点X坐标 |
| triggerY | 2 | 是 | Number | 触发点Y坐标 |
| revealedBlocks | - | 是 | Array | 连锁揭示的方块列表，参考RevealedBlock结构 |

#### RevealedBlock结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | 3 | 是 | Number | 方块X坐标 |
| y | 2 | 是 | Number | 方块Y坐标 |
| neighborMines | 1 | 是 | Number | 周围地雷数量 |

#### 响应示例

```json
{
  "msgId": "NoticeActionDisplay",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "gameStatus": 0,
    "countDown": 5,
    "playerActions": [
      {
        "userId": "1",
        "x": 3,
        "y": 2,
        "action": 1,
        "score": 15,
        "isFirstChoice": true,
        "result": 2
      },
      {
        "userId": "2",
        "x": 1,
        "y": 4,
        "action": 2,
        "score": 5,
        "isFirstChoice": false,
        "result": "correct_mark"
      }
    ],
    "floodFillResults": [
      {
        "triggerUserId": "1",
        "triggerX": 3,
        "triggerY": 2,
        "revealedBlocks": [
          {"x": 2, "y": 2, "neighborMines": 1},
          {"x": 4, "y": 2, "neighborMines": 0},
          {"x": 3, "y": 1, "neighborMines": 1}
        ]
      }
    ],
    "playerTotalScores": {
      "1": 25,
      "2": 15
    },
    "remainingMines": 8,
    "message": "展示阶段：显示玩家操作结果"
  }
}
```

### 27. 扫雷回合结束通知

**WebSocket消息类型**：`"NoticeRoundEnd"`

服务端通知客户端扫雷游戏回合正式结束。此通知在25秒回合时间结束后自动发送，纯状态通知，确认回合结束。详细的操作数据已在NoticeActionDisplay中发送。

**发送时机**：
- 25秒回合倒计时结束后自动发送
- 在NoticeActionDisplay发送5秒后
- 纯状态通知，确认回合结束
- 准备进入下一回合或游戏结束判断

**接口简化说明**：
- **数据已前置**：详细的操作结果、地图数据等已在NoticeActionDisplay中发送
- **职责分离**：本接口专注于状态通知，不重复发送数据
- **前端优势**：前端在第20秒就获得完整数据，有5秒时间展示动画

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| roundNumber | 1 | 是 | Number | 当前回合编号 |
| gameStatus | 1 | 是 | Number | 游戏状态（1-回合结束） |
| message | "回合结束" | 是 | String | 回合结束消息 |



#### 响应示例

```json
{
  "msgId": "NoticeRoundEnd",
  "code": 0,
  "msg": "success",
  "data": {
    "roundNumber": 1,
    "gameStatus": 1,
    "message": "回合结束"
  }
}
```

---

### 28. 扫雷首选玩家奖励推送通知

**WebSocket消息类型**：`"NoticeFirstChoiceBonus"`

服务端通知客户端首选玩家奖励。此通知在玩家成为首选玩家（第一个在当前回合进行挖掘或标记操作）时立即发送，只包含首选玩家的+1分奖励。

**发送时机**：
- 玩家成为首选玩家时立即发送
- 只发送给首选玩家本人
- 只包含+1分的首选玩家奖励，不包含基础得分
- 与基础得分（挖掘/标记得分）分离推送

**奖励规则**：
- **首选玩家奖励**：固定+1分
- **仅真实玩家操作**：AI托管不享受此奖励
- **无论操作对错**：不管挖掘/标记结果如何都给奖励
- **每回合只有一个**：只有第一个操作的玩家享受

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | "player_001" | 是 | String | 玩家ID |
| roundNumber | 1 | 是 | Number | 回合编号 |
| bonusScore | 1 | 是 | Number | 首选玩家奖励分数（固定+1） |
| totalScore | 15 | 是 | Number | 累计总得分（包含此奖励） |

#### 响应示例

```json
{
  "msgId": "NoticeFirstChoiceBonus",
  "code": 0,
  "msg": "success",
  "data": {
    "userId": "player_001",
    "roundNumber": 1,
    "bonusScore": 1,
    "totalScore": 15
  }
}
```

### 29. 扫雷游戏结算通知

**WebSocket消息类型**：`"Settlement"`

服务端通知客户端扫雷游戏结算结果。此通知在游戏结束时发送，包含金币分配和游戏统计信息。

**发送时机**：
- 游戏结束后立即发送
- 在NoticeGameEnd之前发送
- 包含金币变化和奖励分配信息
- 提供游戏统计数据

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| gameType | "minesweeper" | 是 | String | 游戏类型标识 |
| totalRounds | 5 | 是 | Number | 总回合数 |
| playerCount | 2 | 是 | Number | 玩家数量 |
| finalRanking | - | 是 | Array | 最终排名列表，参考PlayerFinalResult结构 |
| fee | 100 | 是 | Number | 房间费用 |
| gameStats | - | 是 | Object | 游戏统计信息 |

#### GameStats结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| mapSize | "8x8" | 是 | String | 地图大小 |
| mineCount | 13 | 是 | Number | 地雷总数 |
| revealedCount | 45 | 是 | Number | 已揭示的方块数 |

#### 响应示例

```json
{
  "msgId": "Settlement",
  "code": 0,
  "msg": "success",
  "data": {
    "gameType": "minesweeper",
    "totalRounds": 5,
    "playerCount": 2,
    "finalRanking": [
      {
        "userId": "player_001",
        "totalScore": 25,
        "totalCoin": 180,
        "mineHits": 1,
        "rank": 1,
        "coinChg": 180
      },
      {
        "userId": "player_002",
        "totalScore": 15,
        "totalCoin": 0,
        "mineHits": 2,
        "rank": 2,
        "coinChg": 0
      }
    ],
    "fee": 100,
    "gameStats": {
      "mapSize": "8x8",
      "mineCount": 13,
      "revealedCount": 45
    }
  }
}
```

### 30. 扫雷游戏结束通知

**WebSocket消息类型**：`"NoticeGameEnd"`

服务端通知客户端扫雷游戏结束。此通知在满足游戏结束条件后发送，包含最终排名和完整地图信息。

**游戏结束条件**（满足任一条件即可）：
1. **所有安全区被挖掘**：所有非地雷格子都被至少一个玩家挖掘
2. **剩余地雷全部标记准确**：
   - 所有未挖掘的地雷都被正确标记
   - 没有安全区被错误标记为地雷

**发送时机**：
- 满足游戏结束条件后立即发送
- 包含最终排名和总得分
- 显示完整的地图信息（包括所有地雷位置）
- 开始10秒游戏结束展示倒计时

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| gameStatus | 2 | 是 | Number | 游戏状态（2-游戏结束） |
| countDown | 10 | 是 | Number | 游戏结束展示倒计时（10秒） |
| finalRanking | [] | 是 | Array | 最终排名列表，参考PlayerFinalResult结构 |
| completeMapData | [] | 是 | Array | 完整地图数据（显示所有地雷和信息），根据地图类型返回不同结构 |
| totalRounds | 1 | 是 | Number | 总回合数 |
| remainingMines | 0 | 是 | Number | 剩余地雷数量（游戏结束时应该为0） |

#### PlayerFinalResult结构

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | "player_001" | 是 | String | 玩家ID |
| totalScore | 15 | 是 | Number | 总得分（用于排名计算） |
| totalCoin | 150 | 是 | Number | 获得的金币数量（用于前端显示） |
| mineHits | 2 | 是 | Number | 踩雷数（用于排名，标记后踩雷不计入） |
| rank | 1/2/3/4 | 是 | Number | 最终排名 |
| coinChg | 180 | 是 | Number | 金币变化（正数为获得，负数为损失，0为无变化） |

#### CompleteMapData结构

`completeMapData`的数据结构根据地图类型而不同：

##### 方格地图（mapType=0）
返回二维数组，表示完整的8×8地图数据。数组结构为`completeMapData[行][列]`，其中每个方块包含以下信息：

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| x | 3 | 是 | Number | 方块X坐标（0-7，从左到右） |
| y | 2 | 是 | Number | 方块Y坐标（0-7，从下到上，左下角为(0,0)） |
| isMine | true | 是 | Boolean | 是否是地雷（游戏结束时显示所有地雷） |
| isRevealed | false | 是 | Boolean | 是否已被揭示 |
| isMarked | true | 是 | Boolean | 是否被标记为地雷 |
| neighborMines | 2 | 是 | Number | 周围地雷数量（游戏结束时显示所有方块的周围地雷数） |
| players | ["player_001"] | 是 | Array | 操作过该方块的玩家ID列表 |

**数据结构示例**：
```json
{
  "completeMapData": [
    [
      {
        "x": 0, "y": 7, "isMine": false, "isRevealed": true,
        "isMarked": false, "neighborMines": 1, "players": ["player_001"]
      },
      {
        "x": 1, "y": 7, "isMine": true, "isRevealed": false,
        "isMarked": true, "neighborMines": 0, "players": ["player_002"]
      }
    ],
    // ... 其他行数据
  ]
}
```

**数组结构说明**：

- 数组采用行列存储：`completeMapData[行][列]`
- 第一个中括号表示**行**，第二个中括号表示**列**
- 游戏坐标系统：左下角为(0,0)，Y轴从下往上递增
- 数组与游戏坐标的直接对应关系：
  - `[0][0]` → 游戏坐标(0,0) **左下角**
  - `[0][7]` → 游戏坐标(7,0) **右下角**
  - `[7][0]` → 游戏坐标(0,7) **左上角**
  - `[7][7]` → 游戏坐标(7,7) **右上角**
- 游戏结束时，所有方块的`isMine`和`neighborMines`信息都会完整显示
- `players`数组记录了所有操作过该方块的玩家，用于多人协作分析

##### 六边形地图（mapType=1）
返回一维数组，包含所有六边形方块的信息。每个六边形方块包含以下信息：

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| q | 0 | 是 | Number | 六边形Q坐标（轴向坐标系统） |
| r | 0 | 是 | Number | 六边形R坐标（轴向坐标系统） |
| isMine | true | 是 | Boolean | 是否是地雷（游戏结束时显示所有地雷） |
| isRevealed | false | 是 | Boolean | 是否已被揭示 |
| isMarked | true | 是 | Boolean | 是否被标记为地雷 |
| neighborMines | 2 | 是 | Number | 周围地雷数量（游戏结束时显示所有方块的周围地雷数） |
| players | ["player_001"] | 是 | Array | 操作过该方块的玩家ID列表 |

**六边形地图数据结构示例**：
```json
{
  "completeMapData": [
    {
      "q": 0, "r": 0, "isMine": false, "isRevealed": true,
      "isMarked": false, "neighborMines": 2, "players": ["player_001"]
    },
    {
      "q": 1, "r": 0, "isMine": true, "isRevealed": false,
      "isMarked": true, "neighborMines": 0, "players": ["player_002"]
    },
    {
      "q": -1, "r": 1, "isMine": false, "isRevealed": false,
      "isMarked": false, "neighborMines": 1, "players": []
    }
  ]
}
```

#### 响应示例

##### 方格地图响应示例
```json
{
  "msgId": "NoticeGameEnd",
  "code": 0,
  "msg": "success",
  "data": {
    "gameStatus": 2,
    "countDown": 10,
    "finalRanking": [
      {
        "userId": "player_001",
        "totalScore": 15,
        "totalCoin": 180,
        "mineHits": 1,
        "rank": 1,
        "coinChg": 180
      }
    ],
    "remainingMines": 0,
    "completeMapData": [
      [
        {"x": 0, "y": 0, "isMine": false, "isRevealed": true, "isMarked": false, "neighborMines": 2, "players": ["player_001"]},
        {"x": 1, "y": 0, "isMine": true, "isRevealed": false, "isMarked": true, "neighborMines": 0, "players": ["player_002"]}
      ]
    ],
    "totalRounds": 1
  }
}
```

##### 六边形地图响应示例
```json
{
  "msgId": "NoticeGameEnd",
  "code": 0,
  "msg": "success",
  "data": {
    "gameStatus": 2,
    "countDown": 10,
    "finalRanking": [
      {
        "userId": "player_001",
        "totalScore": 15,
        "totalCoin": 180,
        "mineHits": 1,
        "rank": 1,
        "coinChg": 180
      }
    ],
    "remainingMines": 0,
    "completeMapData": [
      {"q": 0, "r": 0, "isMine": false, "isRevealed": true, "isMarked": false, "neighborMines": 2, "players": ["player_001"]},
      {"q": 1, "r": 0, "isMine": true, "isRevealed": false, "isMarked": true, "neighborMines": 0, "players": ["player_002"]},
      {"q": -1, "r": 1, "isMine": false, "isRevealed": false, "isMarked": false, "neighborMines": 1, "players": []}
    ],
    "totalRounds": 1
  }
}
```

### 29. 金币变化通知

**WebSocket消息类型**：`"NoticeUserCoin"`

服务端通知客户端玩家金币变化。

#### 通知参数

| 参数名 | 参数值 | 是否必填 | 参数类型 | 描述说明 |
|--------|--------|----------|----------|----------|
| userId | - | 是 | String | 玩家ID |
| coin | - | 是 | Number | 玩家最新的金币 |

## 数据结构定义

### VoiceRoomUser 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| userId | String | 玩家id |
| nickName | String | 昵称 |
| avatar | String | 头像 |
| pos | Number | 座位号 |
| ready | Boolean | 是否准备 |
| robot | Boolean | 是否机器人 |
| role | String | 角色 |

### RoomUser 结构

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| userId | String | 是 | 用户ID |
| nickName | String | 是 | 昵称 |
| avatar | String | 是 | 头像 |
| pos | Number | 是 | 座位号 |
| coin | Number | 是 | 玩家最新金币 |
| status | Number | 是 | 玩家状态（1-站立，2-已坐下，3-游戏中） |

**蛇梯棋游戏相关字段（可选，保持向后兼容）**：

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| ownProp | Number | 否 | 当前持有的道具(同一时间只能拥有一个) |
| curChessPos | Number | 否 | 当前棋子位置 |
| isShield | Boolean | 否 | 当前是否使用了盾牌 |
| dicePoint | Number | 否 | 最近一次掷骰子点数 |
| effectProps | Array | 否 | 最近一次掷骰子有效的道具列表 |
| skinChessId | Number | 否 | 棋子的皮肤Id |

**扫雷游戏相关字段（可选）**：

| 参数名 | 参数类型 | 是否必填 | 描述说明 |
|--------|----------|----------|----------|
| minesFound | Number | 否 | 已找到的地雷数量 |
| blocksRevealed | Number | 否 | 已揭开的安全块数量 |
| gameScore | Number | 否 | 当前游戏得分 |
| isAlive | Boolean | 否 | 是否还在游戏中（未踩到地雷） |

### InviteInfo 结构

| 参数名 | 参数类型 | 描述说明 |
|--------|----------|----------|
| inviteCode | Number | 邀请码 |
| playerNum | Number | 玩家人数 |
| mapType | Number | 地图类型（0-方格地图，1-六边形地图） |
| fee | Number | 房间费用 |
| users | Array | 邀请房间中的玩家列表 |

## 错误码说明

| 错误码 | 错误信息 | 描述 |
|--------|----------|------|
| 0 | 成功 | 操作成功 |
| 1 | 错误的GameId | 游戏ID错误 |
| 6 | 玩家已经在匹配队列中 | 重复匹配 |
| 7 | 没有足够的金币 | 金币不足 |
| 10 | 没有找到玩家信息 | 玩家不存在 |
| 13 | 请求参数错误 | 参数验证失败 |
| 21 | 游客被限制 | 游客无法进行此操作 |
| 32 | 玩家已经在游戏中了 | 重复进入游戏 |

## 游戏状态说明

**扫雷游戏状态（mapType=0）**：

| 状态值 | 状态名称 | 描述 |
|--------|----------|------|
| 0 | 扫雷进行中 | 扫雷游戏进行中，玩家可以进行操作 |
| 1 | 回合结束展示 | 回合结束，展示所有玩家操作结果 |
| 2 | 游戏结束 | 扫雷游戏已结束 |

## 地图类型说明

| 类型值 | 类型名称 | 描述 |
|--------|----------|----------|
| 0 | 方格地图 | 8×8方格布局，13个地雷随机分布 |
| 1 | 六边形地图 | 每一块为六边形布局的地图 |

## 扫雷游戏操作类型说明

| 操作值 | 操作名称 | 描述 |
|--------|----------|------|
| 1 | 挖掘 | 挖掘方块，揭示内容（安全区+6分，地雷-12分） |
| 2 | 标记地雷 | 标记或取消标记地雷（正确标记+10分，错误标记+0分） |

## 扫雷游戏时间配置说明

| 配置项 | 时间（秒） | 描述 |
|--------|------------|------|
| 游戏开始延迟 | 2 | GameStart后的展示时间 |
| 回合总时间 | 25 | 每回合的总操作时间 |
| 隐藏期 | 20 | 前20秒只能看到自己的操作 |
| 展示期 | 5 | 后5秒显示所有人的操作 |

## 扫雷游戏得分规则说明

| 操作结果 | 得分 | 描述 |
|----------|------|------|
| 挖掘安全区 | +6分 | 成功挖掘到非地雷方块 |
| 挖掘地雷区 | -12分 | 不幸挖掘到地雷 |
| 正确标记地雷 | +10分 | 标记的方块确实是地雷 |
| 错误标记 | +0分 | 标记的方块不是地雷 |

**注意**：
- 多个玩家可以选择同一个方块，都会获得相同的得分
- 每回合每个玩家只能操作一次
- 游戏结束条件：所有非地雷方块都被至少一个玩家挖掘过
