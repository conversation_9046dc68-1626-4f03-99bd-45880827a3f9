package roommgr

import (
	"minesweep/common/logx"
	"minesweep/common/safe"
	"minesweep/constvar"
	"minesweep/ecode"

	"ms-version.soofun.online/wjl/game_public/types_public"
)

// SetSettlement 游戏大结算（扫雷游戏专用）
func (slf *Room) SetSettlement() {
	defer func() {
		if slf.voiceRoom != nil {
			slf.voiceRoom.NoticeGameEnd()
		}
	}()

	// 防止重复结算
	if slf.isSettled {
		logx.Infof("RoomID:%v 已经结算过，跳过重复结算", slf.RoomID)
		return
	}

	slf.isSettled = true

	logx.Infof("开始扫雷游戏大结算 RoomID:%v", slf.RoomID)

	// 获取最终排名（如果已经计算过）
	var finalRanking []*PlayerFinalResult
	if slf.finalRanking != nil {
		finalRanking = slf.finalRanking
	} else {
		// 如果没有预先计算的排名，现在计算
		finalRanking = slf.calculateFinalRanking()
	}

	if len(finalRanking) == 0 {
		logx.Errorf("扫雷游戏结算失败：排名列表为空 RoomID:%v", slf.RoomID)
		return
	}

	// 执行扫雷游戏结算（包含金币结算和消息广播）
	slf.performMinesweeperSettlement(finalRanking)

	logx.Infof("扫雷游戏大结算完成 RoomID:%v PlayerCount:%d", slf.RoomID, len(finalRanking))
}

// ForceCloseRoom 强制关闭房间
func (slf *Room) ForceCloseRoom() {
	if slf.IsRefuseService() {
		logx.Infof("RoomID:%v ForceCloseRoom have refuseService", slf.RoomID)
		return
	}
	logx.Infof("RoomID:%v ForceCloseRoom begin, gameStatus:%v", slf.RoomID, slf.gameStatus)
	slf.SetRefuseService() // 标记房间拒绝服务

	// 打印房间
	slf.PrintRoom()

	// 游戏中，归还房间费
	if slf.gameStatus > 0 {
		// 定先手游戏状态，游戏强制提前结算(需要初始化slf.allPlayerInfo)
		if slf.gameStatus == constvar.GameStatusFirstMove {
			var sitUsers []*RoomUser
			for _, v := range slf.allUser {
				if v.Pos < 0 {
					continue
				}
				sitUsers = append(sitUsers, v)
			}
			if len(sitUsers) == slf.playerNum {
				for i := 0; i < slf.playerNum; i++ {
					slf.allPlayerInfo[i].SetUser(sitUsers[i])
				}
			}
		}
		for pos := 0; pos < slf.playerNum; pos++ {
			if len(slf.allPlayerInfo[pos].UserID) <= 0 || slf.fee <= 0 {
				continue
			}

			var coinChg = int64(slf.fee) // 房间费
			_, errCode := slf.ChangeBalance(slf.allPlayerInfo[pos].UserID, int(types_public.ActionEventTwo), coinChg, "settle", "settle", constvar.CoinChangeTypeForceCloseRoom, 0, 0)
			if errCode != ecode.OK {
				logx.Errorf("RoomID:%v userID:%v ForceCloseRoom ChangeBalance failed, coinChg:%v, errCode:%v", slf.RoomID, slf.allPlayerInfo[pos].UserID, coinChg, errCode)
			}
		}

		// 上报游戏结束信息
		if slf.voiceRoom != nil {
			slf.voiceRoom.NoticeGameEnd()
			// todo 没有上报游戏结束
		}
	}

	// 移除所有玩家
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		slf.RemoveUser(v.UserID)
	}
	slf.gameStatus = 0 // 因为RemoveRoom是异步的，所以需要把状态置为0，这样定时器就状态检查就不执行了

	// 移除房间
	safe.Go(func() {
		GetInstance().RemoveRoom(slf.RoomID)
		logx.Infof("RoomID:%v ForceCloseRoom success", slf.RoomID)
	})
}
