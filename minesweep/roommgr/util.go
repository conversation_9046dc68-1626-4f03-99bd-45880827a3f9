package roommgr

import (
	"errors"
	"minesweep/common/logx"
	"minesweep/common/msg"
	"minesweep/common/platform"
	"minesweep/common/safe"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/model"
	"minesweep/model/common/base"
	"minesweep/model/dao"
	"minesweep/usermgr"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"ms-version.soofun.online/wjl/game_public/external"
	"ms-version.soofun.online/wjl/game_public/types_public"
)

// SetGameStatus 设置游戏状态
func (slf *Room) SetGameStatus(gameStatus constvar.GameStatus, countDown int) {
	slf.gameStatus = gameStatus
	slf.countDown = countDown
	slf.ticker.Reset(time.Second)
}

// UpdateLastMsg 更新最新消息
func (slf *Room) UpdateLastMsg(user *RoomUser) {
	user.IsOffline = false
	user.LastMsgTime = time.Now().Unix()
	slf.LastMsgTime = time.Now().Unix()
	// 取消超时
	if user.Pos >= 0 {
		slf.allPlayerInfo[user.Pos].UpdateTimeout(OpByClient)
	}
}

// sendMsgTo 发送消息给某玩家
func (slf *Room) sendMsgTo(userID string, msgID string, errCode int, msgObj any) {
	user := usermgr.GetInstance().GetUserById(slf.appChannel, slf.appID, userID)
	if user != nil && !slf.isStopped {
		user.SendMessage(msgID, errCode, msgObj)
	}
}

// NotifyTo 发送消息给某玩家
func (slf *Room) NotifyTo(userID string, msgID string, errCode int, msgObj any) {
	user := slf.GetUser(userID)
	if user != nil && !user.IsRobot() && !user.isOffline() {
		slf.sendMsgTo(userID, msgID, errCode, msgObj)
	}
}

// Broadcast 广播消息
func (slf *Room) Broadcast(msgID string, errCode int, msgObj any) {
	if slf.voiceRoom == nil {
		var allUser = slf.GetAllUser()
		var sentCount = 0
		for userID, user := range allUser {
			if !user.IsRobot() && !user.isOffline() {
				slf.sendMsgTo(userID, msgID, errCode, msgObj)
				sentCount++
			}
		}
		logx.Infof("广播消息完成 RoomID:%v MsgID:%s TotalUsers:%d SentCount:%d",
			slf.RoomID, msgID, len(allUser), sentCount)
		return
	}

	slf.allConnSrv.Range(func(key, value any) bool {
		connSrvID := key.(string)
		// 不能传用户ID
		_ = dao.GroupDao.GameAckList.Add(connSrvID, &base.GameAckMsg{
			AppChannel: slf.appChannel,
			AppID:      slf.appID,
			PlatRoomID: slf.voiceRoom.GetVoiceRoomId(),
			Data: &msg.ToClientMsg{
				MsgID: msgID,
				Code:  errCode,
				Msg:   ecode.GetMsg(errCode),
				Data:  msgObj,
			},
		})
		return true
	})
}

// BroadcastExcept 广播除某人外消息
func (slf *Room) BroadcastExcept(userID, msgID string, errCode int, msgObj any) {
	if slf.voiceRoom == nil {
		var allUser = slf.GetAllUser()
		for uid, user := range allUser {
			if uid != userID && !user.IsRobot() && !user.isOffline() {
				slf.sendMsgTo(uid, msgID, errCode, msgObj)
			}
		}
		return
	}

	slf.allConnSrv.Range(func(key, value any) bool {
		connSrvID := key.(string)
		// 不能传用户ID
		_ = dao.GroupDao.GameAckList.Add(connSrvID, &base.GameAckMsg{
			AppChannel: slf.appChannel,
			AppID:      slf.appID,
			PlatRoomID: slf.voiceRoom.GetVoiceRoomId(),
			ExpectID:   userID,
			Data: &msg.ToClientMsg{
				MsgID: msgID,
				Code:  errCode,
				Msg:   ecode.GetMsg(errCode),
				Data:  msgObj,
			},
		})
		return true
	})
}

// ChangeBalance 改变玩家金币
func (slf *Room) ChangeBalance(userID string, actionEvent int, coinChg int64, extend string, msgType string, coinChgType constvar.CoinChgType, tax int, gameRank int) (newCoin int64, errCode int) {
	defer func() {
		logx.Infof("RoomID:%v userID:%v ChangeBalance coinChg:%v_%v, newCoin:%v, errCode:%v", slf.RoomID, userID, coinChg, coinChgType, newCoin, errCode)
		if errCode != ecode.OK {
			logx.Errorf("RoomID:%v userID:%v ChangeBalance failed, coinChg:%v_%v, newCoin:%v,errCode:%v, params:%v_%v_%v", slf.RoomID, userID, coinChg, coinChgType, newCoin, errCode, actionEvent, extend, msgType)
		}
	}()

	roomUser := slf.GetUser(userID)
	if roomUser == nil {
		return 0, ecode.ErrNotFoundUser
	}

	var robotPool int64
	if coinChg != 0 {
		if roomUser.IsRobot() {
			if coinChg < 0 && roomUser.Coin+coinChg < 0 {
				return 0, ecode.ErrNotEnoughCoin
			}
			robotPool, _ = dao.GroupDao.Jackpot.Update(slf.appChannel, slf.appID, coinChg)
			roomUser.Coin += coinChg
		} else {
			var resp *external.ZegoBalanceRes
			var err error
			if coinChg < 0 {
				actionEvent = int(types_public.ActionEventOne)
			}
			switch slf.coinType {
			case 0:
				resp, err = platform.ChangeBalance(slf.appID, userID, roomUser.SSToken, slf.appChannel, actionEvent, int(coinChg), roomUser.PlatRoomID, slf.GetRoundID(), extend)
				if err != nil {
					logx.Errorf("RoomID:%v userID:%v ChangeBalance err:%v", slf.RoomID, userID, err)
					return 0, ecode.ErrChangeBalance
				}
			default:
				resp, err = platform.ChangeBalanceByType(slf.appID, userID, roomUser.SSToken, slf.appChannel, actionEvent, int(coinChg), roomUser.PlatRoomID,
					strconv.Itoa(int(slf.RoomID)), extend, msgType, slf.coinType)
				if err != nil {
					logx.Errorf("RoomID:%v userID:%v ChangeBalanceByType err:%v", slf.RoomID, userID, err)
					return 0, ecode.ErrChangeBalance
				}
			}

			if resp.Code != 0 {
				if resp.Code >= 1001 && resp.Code <= 1018 {
					return 0, ecode.ErrChangeBalance
				}
				return 0, int(resp.Code)
			}

			// 更新玩家金币
			user := usermgr.GetInstance().GetUserById(slf.appChannel, slf.appID, userID)
			if user != nil {
				user.Coin = int64(resp.NewBalance)
			}
			roomUser.Coin = int64(resp.NewBalance)
		}
	}

	// 抽水
	var taxPool int64
	if tax > 0 {
		taxPool, _ = dao.GroupDao.TaxPool.Update(slf.appChannel, slf.appID, int64(tax))
	}

	// 保存金币变化
	_ = dao.GroupDao.GameRecordPersist.Add(&model.GameRecord{
		AppChannel:  slf.appChannel,
		AppID:       slf.appID,
		UserID:      userID,
		IsRobot:     roomUser.IsRobot(),
		ChangeType:  string(coinChgType),
		ChangeCount: int(coinChg),
		ChangeTime:  time.Now(),
		RoundID:     slf.GetRoundID(),
		ResultCoin:  roomUser.Coin,
		GameRank:    gameRank,
		Tax:         tax,
		TaxPool:     taxPool,
		RobotPool:   robotPool,
		RoomType:    slf.RoomType,
	})
	return roomUser.Coin, ecode.OK
}

// UpdateBalance 更新玩家金币
func (slf *Room) UpdateBalance(userID string) (int64, int) {
	roomUser := slf.GetUser(userID)
	if roomUser == nil {
		return 0, ecode.ErrNotFoundUser
	}

	// 获取平台玩家资产
	resp, err := platform.GetBalanceInfo(userID, slf.appID, slf.appChannel, roomUser.SSToken, roomUser.ClientIp)
	if err != nil {
		return 0, ecode.ErrRequestUser
	}
	if resp.Code != 0 {
		if resp.Code >= 1001 && resp.Code <= 1018 {
			return 0, ecode.ErrRequestUser
		}
		return 0, int(resp.Code)
	}

	roomUser.Coin = resp.Data.CurrencyBalance
	if slf.coinType != 0 {
		for _, balanceList := range resp.Data.BalanceList {
			if balanceList.CurrencyType == slf.coinType {
				roomUser.Coin = int64(balanceList.CurrencyAmount)
			}
		}
	}

	// 更新玩家金币
	user := usermgr.GetInstance().GetUserById(slf.appChannel, slf.appID, userID)
	if user != nil {
		user.Coin = roomUser.Coin
	}
	return roomUser.Coin, ecode.OK
}

// KickOfflineUsers 踢出所有离线玩家
func (slf *Room) KickOfflineUsers() {
	var nowTime = time.Now().Unix()
	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		if v.isOffline() ||
			(!v.IsRobot() && nowTime-v.LastMsgTime > 120) {
			// 离线或超过2分钟无消息，踢出
			slf.RemoveUser(v.UserID)
		}
	}
}

// CheckUsersCoin 检查座位上玩家金币是否足够
func (slf *Room) CheckUsersCoin() {
	allUser := slf.GetAllUser()
	for _, v := range allUser {
		if v.Coin < int64(slf.fee) {
			slf.RemoveUser(v.UserID)
		}
	}
}

// CheckRecycleRoom 检查是否回收房间
func (slf *Room) CheckRecycleRoom() bool {
	var playerCount, viewerCount int
	for _, v := range slf.allUser {
		if v.IsRobot() {
			continue
		}
		if v.Pos < 0 {
			viewerCount++
			continue
		}
		playerCount++
	}
	if playerCount+viewerCount > 0 {
		return false
	}

	// 移除房间
	safe.Go(func() {
		GetInstance().RemoveRoom(slf.RoomID)
		logx.Infof("RoomID:%v RecycleCloseRoom success", slf.RoomID)
	})
	return true
}

// ResetGame 重置游戏数据
func (slf *Room) ResetGame() {
	slf.isSettled = false
	slf.rollTimes = 0
	slf.curTokenPos = 0
	for i := 0; i < slf.playerNum; i++ {
		slf.allPlayerInfo[i].Reset()
	}
}

// IsOffline 玩家是否离线
func (slf *Room) IsOffline(userID string) bool {
	user := slf.GetUser(userID)
	if user != nil && user.isOffline() {
		return true
	}
	return false
}

// GetUserCoin 获取玩家最新金币
func (slf *Room) GetUserCoin(userID string) int64 {
	roomUser := slf.GetUser(userID)
	if roomUser == nil {
		return 0
	}
	return roomUser.Coin
}

// SetRobotLevel 根据奖池，设置机器人等级
func (slf *Room) SetRobotLevel() {
	_, robotCount, _ := slf.GetPlayerCount()
	if robotCount <= 0 {
		return
	}

	if slf.fee <= 0 {
		var allUser = slf.GetAllUser()
		for _, v := range allUser {
			v.setRobotLevel(constvar.RobotLevelEasy)
		}
		return
	}

	curJackpot, err := dao.GroupDao.Jackpot.Get(slf.appChannel, slf.appID)
	if errors.Is(err, redis.Nil) {
		curJackpot = slf.channelCfg.BaseJackpot
		_ = dao.GroupDao.Jackpot.Set(slf.appChannel, slf.appID, slf.channelCfg.BaseJackpot)
	}

	var robotLevel constvar.RobotLevel
	if curJackpot < slf.channelCfg.BaseJackpot {
		robotLevel = constvar.RobotLevelHard
	} else {
		robotLevel = constvar.RobotLevelEasy
	}

	var allUser = slf.GetAllUser()
	for _, v := range allUser {
		if !v.setRobotLevel(robotLevel) {
			continue
		}
		logx.Infof("RoomID:%v curJackpot:%v, baseJackpot:%v, userID:%v, robotLevel:%v", slf.RoomID, curJackpot, slf.channelCfg.BaseJackpot, v.UserID, v.RobotLevel)
		robotLevel--
	}
}

// GetJackPotProfit 获取当前奖池盈利
func (slf *Room) GetJackPotProfit() int64 {
	curJackpot, err := dao.GroupDao.Jackpot.Get(slf.appChannel, slf.appID)
	if errors.Is(err, redis.Nil) {
		curJackpot = slf.channelCfg.BaseJackpot
		_ = dao.GroupDao.Jackpot.Set(slf.appChannel, slf.appID, slf.channelCfg.BaseJackpot)
	}
	return curJackpot - slf.channelCfg.BaseJackpot
}

// RandNum 生成随机数[0, count-1]
func (slf *Room) RandNum(count int) int {
	return int(slf.unSafeRand.Int63() % int64(count))
}

// Rand 返回闭区间[min,max]
func (slf *Room) Rand(min, max int) int {
	if min == max {
		return min
	}
	return slf.RandNum(max-min+1) + min
}
