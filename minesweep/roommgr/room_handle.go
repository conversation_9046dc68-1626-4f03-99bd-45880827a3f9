package roommgr

import (
	"minesweep/common/logx"
	"minesweep/constvar"
	"minesweep/ecode"
	"minesweep/localmgr"
	"minesweep/model/common/base"
	"minesweep/model/common/request"
	"minesweep/model/common/response"
	"minesweep/usermgr"
	"sort"
	"time"

	"github.com/mitchellh/mapstructure"
)

// ProcessMessage 处理客户端请求
func (slf *Room) ProcessMessage(msg *request.PackMessage) {
	switch msg.MsgID {
	case constvar.MsgTypeBuyProduct:
		slf.OnBuyProduct(msg)
	case constvar.MsgTypeSetSkin:
		slf.OnSetSkin(msg)
	case constvar.MsgTypeViewerList:
		slf.OnViewerList(msg)
	case constvar.MsgTypeLeaveRoom:
		slf.OnUserLeave(msg)
	case constvar.MsgTypeUserOffline:
		slf.OnUserOffline(msg)
	case constvar.MsgTypeEnterRoom:
		slf.OnEnterRoom(msg)
	case constvar.MsgTypeFirstMoveEnd:
		slf.OnUserFirstMoveEnd(msg)
	case constvar.MsgTypeRollDice:
		slf.OnUserRollDice(msg)
	case constvar.MsgTypeMoveChessEnd:
		slf.OnUserMoveChessEnd(msg)
	case constvar.MsgTypeChoiceProp:
		slf.OnUserChoiceProp(msg)
	case constvar.MsgTypeUseProp:
		slf.OnUserUseProp(msg)
	case constvar.MsgTypeChoiceAdvance:
		slf.OnUserChoiceAdvance(msg)
	case constvar.MsgTypeReqSmartOp:
		slf.OnReqSmartOp(msg)
	case constvar.MsgTypeForceCloseGame:
		slf.OnForceCloseGame()
	case constvar.MsgTypeForceUserLeave:
		slf.OnUserForceLeave(msg)
	// 扫雷游戏消息路由
	case constvar.MsgTypeClickBlock:
		//处理用户点击方块操作
		slf.OnUserClickBlock(msg)
	default:
	}
}

// OnEnterRoom 断线重连走这个接口，发送桌面的所有信息
func (slf *Room) OnEnterRoom(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 语聊房断线重连，加入旁观者(已经在房间的不会被覆盖)
	if slf.voiceRoom != nil && user.PlatRoomID == slf.voiceRoom.GetVoiceRoomId() {
		slf.ViewerJoin(&RoomUser{
			PlatRoomID: user.PlatRoomID,
			NickName:   user.Nickname,
			Avatar:     user.Avatar,
			UserID:     user.UserID,
		})
	}

	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)
	slf.UpdateAllConnSrv(user.UserID)

	// 更新玩家金币
	slf.UpdateBalance(user.UserID)

	// 返回游戏桌面信息
	var users []*response.RoomUser
	for _, v := range slf.allUser {
		if v.Pos < 0 {
			continue
		}

		var lastEffectProps = make([]constvar.GameProp, 0)
		if len(slf.allPlayerInfo[v.Pos].LastEffectProps) > 0 {
			lastEffectProps = slf.allPlayerInfo[v.Pos].LastEffectProps
		}

		users = append(users, &response.RoomUser{
			UserID:      v.UserID,
			NickName:    v.NickName,
			Avatar:      v.Avatar,
			Pos:         v.Pos,
			Coin:        v.Coin,
			Status:      v.UserStatus,
			OwnProp:     slf.allPlayerInfo[v.Pos].OwnProp,
			CurChessPos: slf.allPlayerInfo[v.Pos].CurChessPos,
			IsShield:    slf.allPlayerInfo[v.Pos].IsUsingProp(constvar.GamePropShield),
			DicePoint:   slf.allPlayerInfo[v.Pos].getLastDicePoint(),
			EffectProps: lastEffectProps,
			SkinChessID: v.SkinChessID,
		})
	}
	sort.Slice(users, func(i, j int) bool {
		return users[i].Pos < users[j].Pos
	})

	var tokenInfo response.TokenInfo
	tokenInfo.ChoiceProps = []constvar.GameProp{}
	tokenInfo.UserID = slf.allPlayerInfo[slf.curTokenPos].UserID
	tokenInfo.EffectProps = slf.getUserEffectProps(slf.allPlayerInfo[slf.curTokenPos].UserID)
	switch slf.gameStatus {
	case constvar.GameStatusRollDice, constvar.GameStatusUseProp:
		tokenInfo.RollTimes = slf.allPlayerInfo[slf.curTokenPos].RollTimes
	case constvar.GameStatusMoveChess:
		tokenInfo.DicePoint = slf.allPlayerInfo[slf.curTokenPos].getLastDicePoint()
	case constvar.GameStatusChoiceProp:
		curBlock := slf.blockMap.getBlock(slf.allPlayerInfo[slf.curTokenPos].CurChessPos)
		if curBlock != nil && len(curBlock.Props) > 0 {
			tokenInfo.ChoiceProps = curBlock.Props
		}
	}

	// 根据地图类型决定返回的数据结构
	var validHexCoords []*response.HexCoord
	switch slf.MapType {
	case constvar.MapTypeGrid:
		// 方形地图：不返回ValidHexCoords（扫雷游戏暂不支持方形地图的具体实现）
		// TODO: 后续可根据需要添加方形地图的扫雷游戏数据结构
	case constvar.MapTypeHexagon:
		// 六边形地图：返回ValidHexCoords
		validHexCoords = slf.getPresetHexCoords()
	}

	// 返回桌面信息
	var resp = &response.EnterRoom{
		RoomID:         slf.RoomID,
		RoomType:       slf.RoomType,
		PlayerNum:      slf.playerNum,
		MapType:        slf.MapType,
		Fee:            slf.fee,
		Users:          users,
		GameStatus:     slf.gameStatus,
		CountDown:      slf.countDown,
		TokenInfo:      tokenInfo,
		ValidHexCoords: validHexCoords,
	}

	logx.Infof("用户断线重连成功 RoomID:%v userID:%v coin:%v", slf.RoomID, user.UserID, roomUser.Coin)
	user.SendMessage(constvar.MsgTypeEnterRoom, ecode.OK, resp)

	// 前端断线重连需要
	time.Sleep(time.Second)
}

// OnUserFirstMoveEnd 玩家先手动画结束
func (slf *Room) OnUserFirstMoveEnd(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusFirstMove {
		logx.Infof("RoomID:%v userID:%v can not firstMoveEnd, gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		return
	}

	if slf.isFirstMoveEnd {
		logx.Infof("RoomID:%v userID:%v can not firstMoveEnd IsFirstMoveEnd:%v", slf.RoomID, user.UserID, slf.isFirstMoveEnd)
		return
	}
	slf.isFirstMoveEnd = true

	logx.Infof("用户先手结束成功 RoomID:%v userID:%v", slf.RoomID, user.UserID)
	user.SendMessage(constvar.MsgTypeFirstMoveEnd, ecode.OK, struct{}{})

	slf.timerTask.Add(500, func() {
		slf.ProcessCheckFirstMove(true)
	})
}

// OnUserRollDice 玩家掷骰子请求
func (slf *Room) OnUserRollDice(msg *request.PackMessage) {
	params := &request.RollDice{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusRollDice ||
		slf.curTokenPos != roomUser.Pos ||
		slf.allPlayerInfo[roomUser.Pos].IsRollDiceEnd {
		logx.Infof("RoomID:%v userID:%v can not rollDice, gameStatus:%v, curTokenPos:%v, IsRollDiceEnd:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos, slf.allPlayerInfo[roomUser.Pos].IsRollDiceEnd)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserRollDice(roomUser.Pos, roomUser.UserID, OpByClient, params.DicePoint)
}

// OnUserMoveChessEnd 玩家移动棋子结束请求
func (slf *Room) OnUserMoveChessEnd(msg *request.PackMessage) {
	params := &request.MoveChessEnd{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}

	if slf.gameStatus != constvar.GameStatusMoveChess {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		return
	}

	if params.GameRollTimes != slf.rollTimes {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, rollTimes:%v, gameRollTimes:%v", slf.RoomID, user.UserID, slf.rollTimes, params.GameRollTimes)
		return
	}

	// 防止前端异常，限制最小移动棋子的时间(最小1秒)
	const moveChessTime = 5 // 移动棋子时间5秒
	if moveChessTime-slf.countDown < 1 {
		logx.Infof("RoomID:%v userID:%v can not moveChessEnd, moveChessTime:%v, countDown:%v", slf.RoomID, user.UserID, moveChessTime, slf.countDown)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	user.SendMessage(constvar.MsgTypeMoveChessEnd, ecode.OK, struct{}{})

	slf.ProcessCheckMoveChess(true)
}

// OnUserChoiceAdvance 玩家选择前进点数
func (slf *Room) OnUserChoiceAdvance(msg *request.PackMessage) {
	params := &request.ChoiceAdvance{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	// 只能选择1-3点数
	if params.DicePoint <= 0 || params.DicePoint >= 4 {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 掷骰子前可使用道具
	if slf.gameStatus != constvar.GameStatusUseProp ||
		slf.curTokenPos != roomUser.Pos ||
		!slf.allPlayerInfo[roomUser.Pos].IsUsingProp(constvar.GamePropAdvancement) {
		logx.Infof("RoomID:%v userID:%v can not choiceAdvance, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserChoiceAdvance(roomUser.Pos, roomUser.UserID, params.DicePoint, OpByClient)
}

// OnUserChoiceProp 玩家挑选道具请求
func (slf *Room) OnUserChoiceProp(msg *request.PackMessage) {
	params := &request.ChoiceProp{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.gameStatus != constvar.GameStatusChoiceProp ||
		slf.curTokenPos != roomUser.Pos {
		logx.Infof("RoomID:%v userID:%v can not choiceProp, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	if slf.allPlayerInfo[roomUser.Pos].OwnProp > 0 {
		logx.Infof("RoomID:%v userID:%v already have prop:%v", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].OwnProp)
		user.SendMessage(msg.MsgID, ecode.ErrAlreadyHaveProp, struct{}{})
		return
	}

	curBlock := slf.blockMap.getBlock(slf.allPlayerInfo[roomUser.Pos].CurChessPos)
	if curBlock == nil {
		logx.Infof("RoomID:%v userID:%v curChessPos:%v no find block", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].CurChessPos)
		return
	}
	if !curBlock.isHaveProp(params.PropType) {
		logx.Infof("RoomID:%v userID:%v curChessPos:%v no prop:%v", slf.RoomID, user.UserID, slf.allPlayerInfo[roomUser.Pos].CurChessPos, params.PropType)
		user.SendMessage(msg.MsgID, ecode.ErrBlockNoSuchProp, struct{}{})
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserChoiceProp(roomUser.Pos, roomUser.UserID, params.PropType, OpByClient)
}

// OnUserUseProp 玩家使用道具请求
func (slf *Room) OnUserUseProp(msg *request.PackMessage) {
	params := &request.UseProp{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	if !params.PropType.Valid() {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 掷骰子前可使用道具
	if slf.gameStatus != constvar.GameStatusRollDice ||
		slf.curTokenPos != roomUser.Pos ||
		slf.allPlayerInfo[roomUser.Pos].RollTimes > 0 {
		logx.Infof("RoomID:%v userID:%v can not use prop, gameStatus:%v, curTokenPos:%v, rollTimes:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos, slf.allPlayerInfo[roomUser.Pos].RollTimes)
		return
	}

	// 是否拥有该道具
	if slf.allPlayerInfo[roomUser.Pos].OwnProp != params.PropType {
		logx.Infof("RoomID:%v userID:%v no prop:%v, ownProp:%v", slf.RoomID, user.UserID, params.PropType, slf.allPlayerInfo[roomUser.Pos].OwnProp)
		user.SendMessage(msg.MsgID, ecode.ErrPropNotFound, struct{}{})
		return
	}

	// 该种道具是否在使用中
	if slf.allPlayerInfo[roomUser.Pos].IsUsingProp(params.PropType) {
		logx.Infof("RoomID:%v userID:%v prop:%v is using", slf.RoomID, user.UserID, params.PropType)
		user.SendMessage(msg.MsgID, ecode.ErrPropUsing, struct{}{})
		return
	}

	// 清空延迟任务
	slf.timerTask.Clear()
	slf.ProcessUserUseProp(roomUser.Pos, roomUser.UserID, params.PropType, OpByClient)
}

// OnReqSmartOp 请求智能操作
func (slf *Room) OnReqSmartOp(msg *request.PackMessage) {
	// 判断房间是否已结算
	if slf.gameStatus <= 0 {
		return
	}

	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("RoomID:%v userID:%v usermgr no user", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 判断是否是该房间的玩家
	roomUser := slf.GetSitUser(user.UserID)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v not sitUser", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	if slf.curTokenPos != roomUser.Pos {
		logx.Infof("RoomID:%v userID:%v can not reqSmartOp, gameStatus:%v, curTokenPos:%v", slf.RoomID, user.UserID, slf.gameStatus, slf.curTokenPos)
		return
	}

	slf.ProcessUserSmartOp(roomUser.Pos, msg.Ext)
}

// OnForceCloseGame 强制关闭游戏
func (slf *Room) OnForceCloseGame() {
	logx.Infof("RoomID:%v OnForceCloseGame", slf.RoomID)
	slf.settleType = base.SettleTypeApiClose
	slf.SwitchToSettlement()
	if slf.voiceRoom != nil && slf.apiScene.IsPK() {
		slf.voiceRoom.CloseVoiceRoom()
	}
}

// OnUserForceLeave 强制玩家离开
func (slf *Room) OnUserForceLeave(msg *request.PackMessage) {
	params := &request.UserLeave{}
	err := mapstructure.Decode(msg.Data, params)
	if err != nil {
		return
	}

	roomUser := slf.GetUser(params.UserId)
	if roomUser == nil {
		logx.Infof("RoomID:%v userID:%v room no user", slf.RoomID, params.UserId)
		return
	}

	logx.Infof("用户强制离开房间成功 RoomID:%v userID:%v", slf.RoomID, roomUser.UserID)
	localmgr.GetInstance().RmvLocalByRoomID(slf.appChannel, slf.appID, roomUser.UserID, slf.RoomID)
	slf.Broadcast(msg.MsgID, ecode.OK, &response.NoticeByUserID{
		UserID: roomUser.UserID,
	})
	// 必须放最后，否则广播不到自己
	roomUser.IsLeave = true
}

// OnUserClickBlock 处理用户点击方块操作
func (slf *Room) OnUserClickBlock(msg *request.PackMessage) {
	user := usermgr.GetInstance().GetUserById(msg.Ext.AppChannel, msg.Ext.AppID, msg.Ext.UserID)
	if user == nil {
		logx.Errorf("用户不存在 RoomID:%v userID:%v", slf.RoomID, msg.Ext.UserID)
		return
	}

	// 检查房间用户（使用标准的GetUser方法）
	roomUser := slf.GetUser(user.UserID)
	if roomUser == nil {
		logx.Errorf("房间用户不存在 RoomID:%v userID:%v", slf.RoomID, user.UserID)
		user.SendMessage(msg.MsgID, ecode.ErrNotFoundUser, struct{}{})
		return
	}
	slf.UpdateLastMsg(roomUser)

	// 检查游戏状态
	if slf.gameStatus != constvar.GameStatusMinesweeping {
		logx.Errorf("游戏状态错误，无法点击方块 RoomID:%v userID:%v gameStatus:%v", slf.RoomID, user.UserID, slf.gameStatus)
		return
	}

	// 检查地图类型（现在支持方格和六边形地图）
	if slf.MapType != constvar.MapTypeGrid && slf.MapType != constvar.MapTypeHexagon {
		logx.Errorf("地图类型错误，不支持的扫雷地图类型 RoomID:%v userID:%v mapType:%v", slf.RoomID, user.UserID, slf.MapType)
		return
	}

	// 解析请求参数
	var req struct {
		X      int `json:"x"`
		Y      int `json:"y"`
		Action int `json:"action"`
	}
	if err := mapstructure.Decode(msg.Data, &req); err != nil {
		logx.Errorf("解析点击方块请求失败 RoomID:%v userID:%v err:%v", slf.RoomID, user.UserID, err)
		return
	}

	// 验证坐标有效性
	if !slf.mineMap.IsValidPosition(req.X, req.Y) {
		logx.Errorf("坐标无效 RoomID:%v userID:%v x:%v y:%v", slf.RoomID, user.UserID, req.X, req.Y)
		return
	}

	// 验证操作类型
	if req.Action != 1 && req.Action != 2 {
		logx.Errorf("操作类型无效 RoomID:%v userID:%v action:%v", slf.RoomID, user.UserID, req.Action)
		return
	}

	// 检查是否在允许操作的时间内（前20秒允许操作，后5秒只展示）
	// countDown从25倒计时到0，当countDown <= 5时表示进入展示阶段
	if slf.countDown <= MinesweeperShowTime {
		logx.Infof("操作时间已结束，当前为展示阶段 RoomID:%v userID:%v countDown:%v",
			slf.RoomID, user.UserID, slf.countDown)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "操作时间已结束")
		return
	}

	// 获取目标方块并验证状态
	block := slf.mineMap.GetBlock(req.X, req.Y)
	if block == nil {
		logx.Errorf("获取方块失败 RoomID:%v userID:%v x:%v y:%v", slf.RoomID, user.UserID, req.X, req.Y)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "方块不存在")
		return
	}

	// 检查方块是否可以进行操作（与机器人逻辑保持一致）
	// 1. 检查方块是否已被揭示（挖掘过）
	if block.IsRevealed {
		logx.Infof("方块已被揭示，拒绝操作 RoomID:%v userID:%v action:%v x:%v y:%v",
			slf.RoomID, user.UserID, req.Action, req.X, req.Y)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "该方块已被挖掘，无法再次操作")
		return
	}

	// 2. 检查方块是否在以前回合被操作过
	// block.Players 在操作阶段只包含以前回合的玩家
	hasHistoryOperation := len(block.Players) > 0
	if hasHistoryOperation {
		logx.Infof("方块在以前回合已被操作，拒绝操作 RoomID:%v userID:%v action:%v x:%v y:%v Players:%v",
			slf.RoomID, user.UserID, req.Action, req.X, req.Y, block.Players)
		user.SendMessage(constvar.MsgTypeClickBlock, ecode.ErrParams, "该方块在以前回合已被操作过")
		return
	}

	// 同一回合内允许所有操作：
	// - 玩家可以覆盖自己的操作（换位置或改操作类型）
	// - 多个玩家可以操作同一方块（给予加分或减分）
	// - 不限制已挖掘方块的标记等操作

	// 记录首选玩家（只记录第一个发送请求的玩家）
	if slf.firstPlayerInRound == "" {
		slf.firstPlayerInRound = user.UserID
		logx.Infof("首选玩家记录 RoomID:%v Round:%d FirstPlayer:%v",
			slf.RoomID, slf.currentRound, user.UserID)
	}

	// 存储玩家操作（覆盖之前的操作）
	action := &RoundAction{
		UserID:    user.UserID,
		X:         req.X,
		Y:         req.Y,
		Action:    req.Action,
		Timestamp: time.Now().Unix(),
		Score:     0, // 回合结束时计算
	}
	slf.roundActions[user.UserID] = action

	// 如果是首选玩家，立即推送首选玩家奖励
	slf.pushFirstChoiceBonusIfApplicable(user.UserID)

	// 响应客户端
	user.SendMessage(msg.MsgID, ecode.OK, map[string]interface{}{})

	// 检查是否所有玩家都已操作完毕，如果是则立即进入展示阶段
	slf.checkAllPlayersOperatedAndTriggerDisplay()

	logx.Infof("用户点击方块操作成功 RoomID:%v userID:%v x:%v y:%v action:%v",
		slf.RoomID, user.UserID, req.X, req.Y, req.Action)
}
