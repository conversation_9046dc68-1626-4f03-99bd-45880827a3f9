package roommgr

import (
	"minesweep/common/logx"
	"minesweep/constvar"
)

// RoundEndPhaseHandler 回合结束阶段处理器
type RoundEndPhaseHandler struct{}

// GetPhaseName 获取阶段名称
func (h *RoundEndPhaseHandler) GetPhaseName() string {
	return "RoundEnd"
}

// Enter 进入回合结束阶段
func (h *RoundEndPhaseHandler) Enter(room *Room) error {
	// 回合结束阶段不需要倒计时，立即处理
	room.countDown = 0
	room.gameStatus = constvar.GameStatusRoundEnd

	logx.Infof("进入回合结束阶段 RoomID:%v Round:%d", room.RoomID, room.currentRound)

	// 立即执行回合结束逻辑
	return h.executeRoundEndLogic(room)
}

// Tick 回合结束阶段的每秒处理
func (h *RoundEndPhaseHandler) Tick(room *Room) error {
	// 回合结束阶段通常不需要持续的定时处理
	// 如果需要展示回合结束信息一段时间，可以在这里添加逻辑
	return nil
}

// Exit 退出回合结束阶段
func (h *RoundEndPhaseHandler) Exit(room *Room) error {
	logx.Infof("退出回合结束阶段 RoomID:%v Round:%d", room.RoomID, room.currentRound)
	return nil
}

// CanTransitionTo 检查是否可以转换到指定阶段
func (h *RoundEndPhaseHandler) CanTransitionTo(newPhase RoomPhase) bool {
	switch newPhase {
	case PhaseWaiting:
		return true // 回合结束可以转换到等待阶段（新回合）
	case PhaseGameEnd:
		return true // 回合结束可以转换到游戏结束阶段
	default:
		return false
	}
}

// executeRoundEndLogic 执行回合结束逻辑
func (h *RoundEndPhaseHandler) executeRoundEndLogic(room *Room) error {
	logx.Infof("执行回合结束逻辑 RoomID:%v Round:%d", room.RoomID, room.currentRound)

	// 1. 广播回合结束消息
	room.broadcastRoundEnd()

	// 2. 检查游戏是否结束
	if room.checkGameEndConditions() {
		// 游戏结束，转换到游戏结束阶段
		return room.phaseManager.TransitionTo(PhaseGameEnd)
	}

	// 3. 游戏继续，开始新回合
	room.startNewRound()

	// 4. 广播新回合开始
	room.broadcastRoundStart()

	// 5. 转换到等待阶段
	return room.phaseManager.TransitionTo(PhaseWaiting)
}

// GameEndPhaseHandler 游戏结束阶段处理器
type GameEndPhaseHandler struct{}

// GetPhaseName 获取阶段名称
func (h *GameEndPhaseHandler) GetPhaseName() string {
	return "GameEnd"
}

// Enter 进入游戏结束阶段
func (h *GameEndPhaseHandler) Enter(room *Room) error {
	room.countDown = 10 // 游戏结束后等待10秒
	room.gameStatus = constvar.GameStatusGameEnd

	logx.Infof("进入游戏结束阶段 RoomID:%v Round:%d", room.RoomID, room.currentRound)

	// 执行游戏结束逻辑
	return h.executeGameEndLogic(room)
}

// Tick 游戏结束阶段的每秒处理
func (h *GameEndPhaseHandler) Tick(room *Room) error {
	// 游戏结束后的倒计时
	room.countDown--

	logx.Debugf("游戏结束倒计时 RoomID:%v CountDown:%d", room.RoomID, room.countDown)

	// 倒计时结束后可以进行清理工作
	if room.countDown <= 0 {
		logx.Infof("游戏结束倒计时完成 RoomID:%v", room.RoomID)
		// 这里可以添加房间清理逻辑
	}

	return nil
}

// Exit 退出游戏结束阶段
func (h *GameEndPhaseHandler) Exit(room *Room) error {
	logx.Infof("退出游戏结束阶段 RoomID:%v", room.RoomID)
	return nil
}

// CanTransitionTo 检查是否可以转换到指定阶段
func (h *GameEndPhaseHandler) CanTransitionTo(newPhase RoomPhase) bool {
	// 游戏结束是最终状态，通常不转换到其他状态
	// 除非需要重新开始游戏
	return false
}

// executeGameEndLogic 执行游戏结束逻辑
func (h *GameEndPhaseHandler) executeGameEndLogic(room *Room) error {
	logx.Infof("执行游戏结束逻辑 RoomID:%v TotalRounds:%d", room.RoomID, room.currentRound)

	// 1. 计算最终排名
	finalRanking := room.calculateFinalRanking()

	// 2. 广播游戏结束消息
	room.broadcastGameEnd(finalRanking)

	// 3. 执行游戏结算
	room.performMinesweeperSettlement(finalRanking)

	logx.Infof("游戏结束逻辑执行完成 RoomID:%v", room.RoomID)
	return nil
}
