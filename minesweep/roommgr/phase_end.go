package roommgr

import (
	"minesweep/common/logx"
	"minesweep/constvar"
)

// RoundEndPhaseHandler 回合结束阶段处理器
type RoundEndPhaseHandler struct{}

// GetPhaseName 获取阶段名称
func (h *RoundEndPhaseHandler) GetPhaseName() string {
	return "RoundEnd"
}

// Enter 进入回合结束阶段
func (h *RoundEndPhaseHandler) Enter(room *Room) error {
	// 回合结束阶段不需要倒计时，立即处理
	room.countDown = 0
	room.gameStatus = constvar.GameStatusRoundEnd

	logx.Infof("进入回合结束阶段 RoomID:%v Round:%d", room.RoomID, room.currentRound)

	// 立即执行回合结束逻辑
	return h.executeRoundEndLogic(room)
}

// Tick 回合结束阶段的每秒处理
func (h *RoundEndPhaseHandler) Tick(room *Room) error {
	// 回合结束阶段通常不需要持续的定时处理
	// 如果需要展示回合结束信息一段时间，可以在这里添加逻辑
	return nil
}

// Exit 退出回合结束阶段
func (h *RoundEndPhaseHandler) Exit(room *Room) error {
	logx.Infof("退出回合结束阶段 RoomID:%v Round:%d", room.RoomID, room.currentRound)
	return nil
}

// CanTransitionTo 检查是否可以转换到指定阶段
func (h *RoundEndPhaseHandler) CanTransitionTo(newPhase RoomPhase) bool {
	switch newPhase {
	case PhaseWaiting:
		return true // 回合结束可以转换到等待阶段（新回合）
	case PhaseGameEnd:
		return true // 回合结束可以转换到游戏结束阶段
	default:
		return false
	}
}

// executeRoundEndLogic 执行回合结束逻辑
func (h *RoundEndPhaseHandler) executeRoundEndLogic(room *Room) error {
	logx.Infof("执行回合结束逻辑 RoomID:%v Round:%d", room.RoomID, room.currentRound)

	// 1. 广播回合结束消息
	room.broadcastRoundEnd()

	// 2. 检查游戏是否结束
	if room.checkGameEndConditions() {
		// 游戏结束，转换到游戏结束阶段
		return room.phaseManager.TransitionTo(PhaseGameEnd)
	}

	// 3. 游戏继续，开始新回合
	room.startNewRound()

	// 4. 转换到等待阶段（startNewRound已经包含了broadcastRoundStart）
	return room.phaseManager.TransitionTo(PhaseWaiting)
}

// GameEndPhaseHandler 游戏结束阶段处理器
type GameEndPhaseHandler struct{}

// GetPhaseName 获取阶段名称
func (h *GameEndPhaseHandler) GetPhaseName() string {
	return "GameEnd"
}

// Enter 进入游戏结束阶段
func (h *GameEndPhaseHandler) Enter(room *Room) error {
	room.countDown = GameEndDisplayTime // 游戏结束后等待10秒
	room.gameStatus = constvar.GameStatusGameEnd

	logx.Infof("进入游戏结束阶段 RoomID:%v Round:%d CountDown:%d",
		room.RoomID, room.currentRound, room.countDown)

	// 只执行游戏结束展示逻辑，不执行结算
	return h.executeGameEndDisplay(room)
}

// Tick 游戏结束阶段的每秒处理
func (h *GameEndPhaseHandler) Tick(room *Room) error {
	// 游戏结束后的倒计时
	room.countDown--

	logx.Debugf("游戏结束倒计时 RoomID:%v CountDown:%d", room.RoomID, room.countDown)

	// 倒计时结束后切换到结算
	if room.countDown <= 0 {
		logx.Infof("游戏结束倒计时完成，切换到结算 RoomID:%v", room.RoomID)
		// 仿照snakes项目，直接调用SwitchToSettlement
		room.SwitchToSettlement()
		return nil
	}

	return nil
}

// Exit 退出游戏结束阶段
func (h *GameEndPhaseHandler) Exit(room *Room) error {
	logx.Infof("退出游戏结束阶段 RoomID:%v", room.RoomID)
	return nil
}

// CanTransitionTo 检查是否可以转换到指定阶段
func (h *GameEndPhaseHandler) CanTransitionTo(newPhase RoomPhase) bool {
	// 游戏结束是最终状态，通常不转换到其他状态
	// 除非需要重新开始游戏
	return false
}

// executeGameEndDisplay 执行游戏结束展示逻辑（立即执行）
func (h *GameEndPhaseHandler) executeGameEndDisplay(room *Room) error {
	logx.Infof("执行游戏结束展示逻辑 RoomID:%v TotalRounds:%d", room.RoomID, room.currentRound)

	// 1. 计算最终排名
	finalRanking := room.calculateFinalRanking()

	// 2. 广播游戏结束消息（包含最终排名）
	room.broadcastGameEnd(finalRanking)

	// 保存最终排名，供后续结算使用
	room.finalRanking = finalRanking

	logx.Infof("游戏结束展示逻辑执行完成 RoomID:%v，等待%d秒后执行结算",
		room.RoomID, room.countDown)
	return nil
}
