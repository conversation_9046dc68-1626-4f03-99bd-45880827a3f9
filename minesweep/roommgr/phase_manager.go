package roommgr

import (
	"fmt"
	"minesweep/common/logx"
)

// RoomPhase 房间阶段枚举
type RoomPhase int

const (
	PhaseWaiting  RoomPhase = iota // 等待玩家操作阶段
	PhaseDisplay                   // 展示阶段
	PhaseRoundEnd                  // 回合结束阶段
	PhaseGameEnd                   // 游戏结束阶段
)

// String 返回阶段的字符串表示
func (p RoomPhase) String() string {
	switch p {
	case PhaseWaiting:
		return "Waiting"
	case PhaseDisplay:
		return "Display"
	case PhaseRoundEnd:
		return "RoundEnd"
	case PhaseGameEnd:
		return "GameEnd"
	default:
		return "Unknown"
	}
}

// PhaseHandler 阶段处理器接口
type PhaseHandler interface {
	// Enter 进入阶段时调用
	Enter(room *Room) error

	// Tick 每秒调用一次，处理阶段内的逻辑
	Tick(room *Room) error

	// Exit 退出阶段时调用
	Exit(room *Room) error

	// CanTransitionTo 检查是否可以转换到指定阶段
	CanTransitionTo(newPhase RoomPhase) bool

	// GetPhaseName 获取阶段名称（用于日志）
	GetPhaseName() string
}

// PhaseManager 阶段管理器
type PhaseManager struct {
	currentPhase RoomPhase
	handlers     map[RoomPhase]PhaseHandler
	room         *Room
}

// NewPhaseManager 创建新的阶段管理器
func NewPhaseManager(room *Room) *PhaseManager {
	pm := &PhaseManager{
		room:         room,
		currentPhase: PhaseWaiting, // 默认从等待阶段开始
		handlers: map[RoomPhase]PhaseHandler{
			PhaseWaiting:  &WaitingPhaseHandler{},
			PhaseDisplay:  &DisplayPhaseHandler{},
			PhaseRoundEnd: &RoundEndPhaseHandler{},
			PhaseGameEnd:  &GameEndPhaseHandler{},
		},
	}
	return pm
}

// GetCurrentPhase 获取当前阶段
func (pm *PhaseManager) GetCurrentPhase() RoomPhase {
	return pm.currentPhase
}

// TransitionTo 转换到指定阶段
func (pm *PhaseManager) TransitionTo(newPhase RoomPhase) error {
	if pm.currentPhase == newPhase {
		// 已经在目标阶段，无需转换
		return nil
	}

	currentHandler := pm.handlers[pm.currentPhase]
	newHandler := pm.handlers[newPhase]

	if newHandler == nil {
		return fmt.Errorf("no handler found for phase %v", newPhase)
	}

	// 检查是否可以转换
	if !currentHandler.CanTransitionTo(newPhase) {
		return fmt.Errorf("invalid transition from %v to %v", pm.currentPhase, newPhase)
	}

	logx.Infof("阶段转换开始 RoomID:%v Round:%d %v -> %v",
		pm.room.RoomID, pm.room.currentRound, pm.currentPhase, newPhase)

	// 退出当前阶段
	if err := currentHandler.Exit(pm.room); err != nil {
		return fmt.Errorf("failed to exit phase %v: %w", pm.currentPhase, err)
	}

	// 更新当前阶段
	oldPhase := pm.currentPhase
	pm.currentPhase = newPhase

	// 进入新阶段
	if err := newHandler.Enter(pm.room); err != nil {
		// 如果进入新阶段失败，回滚到原阶段
		pm.currentPhase = oldPhase
		return fmt.Errorf("failed to enter phase %v: %w", newPhase, err)
	}

	logx.Infof("阶段转换完成 RoomID:%v Round:%d %v -> %v CountDown:%d",
		pm.room.RoomID, pm.room.currentRound, oldPhase, newPhase, pm.room.countDown)

	return nil
}

// Tick 执行当前阶段的定时逻辑
func (pm *PhaseManager) Tick() error {
	handler := pm.handlers[pm.currentPhase]
	if handler == nil {
		return fmt.Errorf("no handler found for current phase %v", pm.currentPhase)
	}

	return handler.Tick(pm.room)
}

// Initialize 初始化阶段管理器（进入初始阶段）
func (pm *PhaseManager) Initialize() error {
	handler := pm.handlers[pm.currentPhase]
	if handler == nil {
		return fmt.Errorf("no handler found for initial phase %v", pm.currentPhase)
	}

	logx.Infof("阶段管理器初始化 RoomID:%v Phase:%v", pm.room.RoomID, pm.currentPhase)
	return handler.Enter(pm.room)
}

// Cleanup 清理阶段管理器
func (pm *PhaseManager) Cleanup() error {
	if pm.currentPhase != PhaseGameEnd {
		// 如果不是在游戏结束阶段，先退出当前阶段
		handler := pm.handlers[pm.currentPhase]
		if handler != nil {
			if err := handler.Exit(pm.room); err != nil {
				logx.Errorf("Failed to exit phase %v during cleanup: %v", pm.currentPhase, err)
			}
		}
	}

	logx.Infof("阶段管理器清理完成 RoomID:%v", pm.room.RoomID)
	return nil
}
