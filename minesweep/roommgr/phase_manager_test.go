package roommgr

import (
	"minesweep/constvar"
	"testing"
)

// createTestRoom 创建测试用的房间
func createTestRoom() *Room {
	room := &Room{
		RoomID:       12345,
		currentRound: 1,
		gameStatus:   constvar.GameStatusMinesweeping,
		countDown:    25,
		allUser:      make(map[string]*RoomUser),
		roundActions: make(map[string]*RoundAction),
	}

	// 添加测试用户
	room.allUser["user1"] = &RoomUser{UserID: "user1", Pos: 0}
	room.allUser["user2"] = &RoomUser{UserID: "user2", Pos: 1}

	return room
}

// TestPhaseManagerCreation 测试状态管理器创建
func TestPhaseManagerCreation(t *testing.T) {
	room := createTestRoom()

	// 创建状态管理器
	pm := NewPhaseManager(room)

	if pm == nil {
		t.Fatal("PhaseManager creation failed")
	}

	if pm.GetCurrentPhase() != PhaseWaiting {
		t.Errorf("Expected initial phase to be PhaseWaiting, got %v", pm.GetCurrentPhase())
	}

	if pm.room != room {
		t.Error("PhaseManager room reference is incorrect")
	}
}

// TestPhaseTransitions 测试状态转换（简化版，不调用Enter/Exit方法）
func TestPhaseTransitions(t *testing.T) {
	room := createTestRoom()
	pm := NewPhaseManager(room)

	// 测试状态管理器的基本功能
	if pm.GetCurrentPhase() != PhaseWaiting {
		t.Errorf("Expected initial phase to be PhaseWaiting, got %v", pm.GetCurrentPhase())
	}

	// 测试状态转换验证逻辑
	waitingHandler := &WaitingPhaseHandler{}

	// 测试有效转换
	if !waitingHandler.CanTransitionTo(PhaseDisplay) {
		t.Error("Expected Waiting -> Display transition to be valid")
	}

	// 测试无效转换
	if waitingHandler.CanTransitionTo(PhaseRoundEnd) {
		t.Error("Expected Waiting -> RoundEnd transition to be invalid")
	}
}

// TestInvalidTransitions 测试非法状态转换
func TestInvalidTransitions(t *testing.T) {
	// 测试各个阶段的转换规则
	waitingHandler := &WaitingPhaseHandler{}
	displayHandler := &DisplayPhaseHandler{}
	roundEndHandler := &RoundEndPhaseHandler{}
	gameEndHandler := &GameEndPhaseHandler{}

	// 等待阶段只能转换到展示阶段或游戏结束
	if !waitingHandler.CanTransitionTo(PhaseDisplay) {
		t.Error("Waiting should be able to transition to Display")
	}
	if !waitingHandler.CanTransitionTo(PhaseGameEnd) {
		t.Error("Waiting should be able to transition to GameEnd")
	}
	if waitingHandler.CanTransitionTo(PhaseRoundEnd) {
		t.Error("Waiting should not be able to transition to RoundEnd")
	}

	// 展示阶段只能转换到回合结束或游戏结束
	if !displayHandler.CanTransitionTo(PhaseRoundEnd) {
		t.Error("Display should be able to transition to RoundEnd")
	}
	if !displayHandler.CanTransitionTo(PhaseGameEnd) {
		t.Error("Display should be able to transition to GameEnd")
	}
	if displayHandler.CanTransitionTo(PhaseWaiting) {
		t.Error("Display should not be able to transition to Waiting")
	}

	// 回合结束阶段可以转换到等待阶段或游戏结束
	if !roundEndHandler.CanTransitionTo(PhaseWaiting) {
		t.Error("RoundEnd should be able to transition to Waiting")
	}
	if !roundEndHandler.CanTransitionTo(PhaseGameEnd) {
		t.Error("RoundEnd should be able to transition to GameEnd")
	}
	if roundEndHandler.CanTransitionTo(PhaseDisplay) {
		t.Error("RoundEnd should not be able to transition to Display")
	}

	// 游戏结束阶段不能转换到其他阶段
	if gameEndHandler.CanTransitionTo(PhaseWaiting) {
		t.Error("GameEnd should not be able to transition to any other phase")
	}
	if gameEndHandler.CanTransitionTo(PhaseDisplay) {
		t.Error("GameEnd should not be able to transition to any other phase")
	}
	if gameEndHandler.CanTransitionTo(PhaseRoundEnd) {
		t.Error("GameEnd should not be able to transition to any other phase")
	}
}

// TestSamePhaseTransition 测试转换到相同阶段
func TestSamePhaseTransition(t *testing.T) {
	room := createTestRoom()
	pm := NewPhaseManager(room)

	// 验证初始状态
	if pm.GetCurrentPhase() != PhaseWaiting {
		t.Errorf("Expected initial phase to be PhaseWaiting, got %v", pm.GetCurrentPhase())
	}

	// 测试相同阶段转换的逻辑（不实际调用TransitionTo避免日志问题）
	// 这里主要测试状态管理器的基本结构
}

// TestPhaseHandlerMethods 测试阶段处理器方法
func TestPhaseHandlerMethods(t *testing.T) {
	// 测试等待阶段处理器
	waitingHandler := &WaitingPhaseHandler{}

	if waitingHandler.GetPhaseName() != "Waiting" {
		t.Errorf("Expected phase name 'Waiting', got '%s'", waitingHandler.GetPhaseName())
	}

	// 测试展示阶段处理器
	displayHandler := &DisplayPhaseHandler{}

	if displayHandler.GetPhaseName() != "Display" {
		t.Errorf("Expected phase name 'Display', got '%s'", displayHandler.GetPhaseName())
	}

	// 测试回合结束阶段处理器
	roundEndHandler := &RoundEndPhaseHandler{}

	if roundEndHandler.GetPhaseName() != "RoundEnd" {
		t.Errorf("Expected phase name 'RoundEnd', got '%s'", roundEndHandler.GetPhaseName())
	}

	// 测试游戏结束阶段处理器
	gameEndHandler := &GameEndPhaseHandler{}

	if gameEndHandler.GetPhaseName() != "GameEnd" {
		t.Errorf("Expected phase name 'GameEnd', got '%s'", gameEndHandler.GetPhaseName())
	}
}
