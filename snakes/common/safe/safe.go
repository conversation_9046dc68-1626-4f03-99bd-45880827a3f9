package safe

import (
	"fmt"
	"runtime/debug"
	"snakes/common/logx"
	"snakes/common/warning"
	"snakes/conf"
)

func Go(fn func()) {
	go func() {
		defer RecoverPanic()
		fn()
	}()
}

func RecoverPanic() {
	if r := recover(); r != nil {
		trace := string(debug.Stack())
		logx.Errorf("panic: %v, stack trace: %v", r, trace)
		msg := fmt.Sprintf("游戏名称:蛇梯, 游戏id:%v, 发生panic:%v, trace:%v", conf.Conf.Server.GameId, r, trace)
		warning.SendWarning(warning.WarningInfo{"预警详情": msg})
		return
	}
}
