package consumer

import (
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"snakes/clientHandle"
	"snakes/common/logx"
	comMsg "snakes/common/msg"
	"snakes/common/redisx"
	"snakes/constvar"
	"snakes/model/common/base"
	"snakes/model/dao"
)

type Subscribe struct {
	userOfflineKey string
}

func NewSubscribe() *Subscribe {
	return &Subscribe{}
}

func (slf *Subscribe) Start() {
	slf.userOfflineKey = dao.UseOfflineKey()
	redisx.Subscribe(context.TODO(), slf.MessageDispatch, slf.userOfflineKey)
}

func (slf *Subscribe) MessageDispatch(msg *redis.Message) error {
	switch msg.Channel {
	case slf.userOfflineKey:
		logx.Infof("Subscribe UserOffline data:%+v", msg.Payload)
		data := &base.UserOffline{}
		err := json.Unmarshal([]byte(msg.Payload), data)
		if err != nil {
			logx.Infof("Subscribe Unmarshal err:%+v", err)
			return err
		}

		// 为了让消息变得有序，放入channel
		req := &base.GameReqMsg{
			AppChannel: data.AppChannel,
			AppID:      data.AppID,
			UserID:     data.UserID,
			Data:       comMsg.FromClientMsg{MsgID: constvar.MsgTypeUserOffline, Data: data},
		}
		field := clientHandle.GetHandler().Field(req.AppChannel, req.AppID, req.UserID)
		ch, err := clientHandle.GetHandler().GetChan(field)
		if err != nil {
			logx.Errorf("OnGameAck GetChan err:%v, field:%v", err, field)
			return err
		}
		ch <- req
	}
	return nil
}
