package clientHandle

import (
	"github.com/mitchellh/mapstructure"
	"snakes/common/logx"
	"snakes/common/platform"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/invitemgr"
	"snakes/model/common/base"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/usermgr"
	"snakes/utils"
	"time"
)

// OnCreateInvite 创建邀请
func (h *Handle) OnCreateInvite(req *base.GameReqMsg) {
	if req.User == nil {
		logx.Errorf("no user param userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrParams)
		return
	}

	params := &request.CreateInvite{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	if req.User.IsVisitor {
		logx.Errorf("User visitor limit userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrVisitorLimit)
		return
	}

	// 添加用户
	user := &usermgr.User{
		UserID:      req.UserID,
		Nickname:    req.User.Nickname,
		Avatar:      req.User.Avatar,
		AppID:       req.AppID,
		AppChannel:  req.AppChannel,
		GameMode:    req.User.GameMode,
		Coin:        req.User.Coin,
		IsVisitor:   req.User.IsVisitor,
		SSToken:     req.User.SSToken,
		ClientIP:    req.User.ClientIP,
		PlatRoomID:  req.PlatRoomID,
		AllProduct:  make(map[constvar.ProductID]bool),
		Role:        req.User.Role,
		ConnSrvID:   req.FromID,
		LastMsgTime: time.Now().Unix(),
	}
	platform.CacheUserInfo(req.UserID, req.User.Coin, req.AppChannel)
	if errCode := user.UpdateBalance(); errCode != ecode.OK {
		logx.Errorf("user.UpdateBalance failed, errCode:%v, user:%+v", errCode, user)
		utils.Fail(req, errCode)
		return
	}
	_ = user.UpdateSkinPackage()

	inviteInfo, errCode := invitemgr.GetInstance().CreateInvite(user, params)
	if errCode != ecode.OK {
		utils.Fail(req, errCode)
		return
	}

	// 创建邀请成功，再加入或覆盖缓存
	usermgr.GetInstance().AddUser(user)

	utils.OkWithDetailed(req, &response.InviteInfo{
		InviteCode: inviteInfo.InviteCode,
		PlayerNum:  inviteInfo.PlayerNum,
		GridNum:    inviteInfo.GridNum,
		Fee:        inviteInfo.Fee,
		PropMode:   inviteInfo.PropMode,
		CreatorID:  inviteInfo.CreatorID,
		Users:      inviteInfo.GetInviteUsers(),
	})
}

// OnAcceptInvite 接受邀请
func (h *Handle) OnAcceptInvite(req *base.GameReqMsg) {
	if req.User == nil {
		logx.Errorf("no user param userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrParams)
		return
	}

	params := &request.AcceptInvite{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	if req.User.IsVisitor {
		logx.Errorf("User visitor limit userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrVisitorLimit)
		return
	}

	// 添加用户
	user := &usermgr.User{
		UserID:      req.UserID,
		Nickname:    req.User.Nickname,
		Avatar:      req.User.Avatar,
		AppID:       req.AppID,
		AppChannel:  req.AppChannel,
		GameMode:    req.User.GameMode,
		Coin:        req.User.Coin,
		IsVisitor:   req.User.IsVisitor,
		SSToken:     req.User.SSToken,
		ClientIP:    req.User.ClientIP,
		PlatRoomID:  req.PlatRoomID,
		AllProduct:  make(map[constvar.ProductID]bool),
		Role:        req.User.Role,
		ConnSrvID:   req.FromID,
		LastMsgTime: time.Now().Unix(),
	}
	platform.CacheUserInfo(req.UserID, req.User.Coin, req.AppChannel)
	if errCode := user.UpdateBalance(); errCode != ecode.OK {
		logx.Errorf("user.UpdateBalance failed, errCode:%v, user:%+v", errCode, user)
		utils.Fail(req, errCode)
		return
	}
	_ = user.UpdateSkinPackage()

	inviteInfo, errCode := invitemgr.GetInstance().AcceptInvite(user, params.InviteCode)
	if errCode != ecode.OK {
		logx.Infof("AcceptInvite failed userID:%v, errCode:%v", req.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
	// 更新玩家在线状态
	_ = invitemgr.GetInstance().UpdateOnlineStatus(user.AppChannel, user.AppID, user.UserID, true)

	// 接受邀请成功，再加入或覆盖缓存
	usermgr.GetInstance().AddUser(user)

	logx.Infof("AcceptInvite appChannel:%v, appID:%v, userID:%v success", req.AppChannel, req.AppID, req.UserID)
	invitemgr.GetInstance().Broadcast(inviteInfo.InviteCode, req.Data.MsgID, ecode.OK, &response.AcceptInvite{
		UserID: user.UserID,
		InviteInfo: response.InviteInfo{
			InviteCode: inviteInfo.InviteCode,
			PlayerNum:  inviteInfo.PlayerNum,
			GridNum:    inviteInfo.GridNum,
			Fee:        inviteInfo.Fee,
			PropMode:   inviteInfo.PropMode,
			CreatorID:  inviteInfo.CreatorID,
			Users:      inviteInfo.GetInviteUsers(),
		},
	})
}

// OnInviteReady 邀请者准备
func (h *Handle) OnInviteReady(req *base.GameReqMsg) {
	params := &request.InviteReady{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	inviteCode, errCode := invitemgr.GetInstance().InviterReady(user.AppChannel, user.AppID, user.UserID, params.Ready)
	if errCode != ecode.OK {
		utils.Fail(req, errCode)
		return
	}

	invitemgr.GetInstance().Broadcast(inviteCode, req.Data.MsgID, errCode, &response.NoticeUserInviteStatus{
		UserID: user.UserID,
		Ready:  params.Ready,
		OnLine: true,
	})
}

// OnChangeInviteCfg 邀请创建者, 更改房间配置
func (h *Handle) OnChangeInviteCfg(req *base.GameReqMsg) {
	params := &request.ChangeInviteCfg{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	errCode := invitemgr.GetInstance().ChangeInviteCfg(user.AppChannel, user.AppID, user.UserID, params)
	if errCode != ecode.OK {
		utils.Fail(req, errCode)
		return
	}

	invitemgr.GetInstance().Broadcast(params.InviteCode, req.Data.MsgID, errCode, &response.ChangeInviteCfg{
		Fee:      params.Fee,
		PropMode: params.PropMode,
	})
}

// OnLeaveInvite 离开邀请
func (h *Handle) OnLeaveInvite(req *base.GameReqMsg) {
	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	errCode := invitemgr.GetInstance().LeaveInvite(user.AppChannel, user.AppID, user.UserID)
	if errCode != ecode.OK {
		utils.Fail(req, errCode)
	}
}

// OnInviteKickOut 创建者踢除玩家
func (h *Handle) OnInviteKickOut(req *base.GameReqMsg) {
	params := &request.InviteKickOut{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	errCode := invitemgr.GetInstance().KickOut(user.AppChannel, user.AppID, user.UserID, params.UserID)
	if errCode != ecode.OK {
		utils.Fail(req, errCode)
	}
}

// OnInviteStart 创建者开始游戏
func (h *Handle) OnInviteStart(req *base.GameReqMsg) {
	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	errCode := invitemgr.GetInstance().StartGame(user.AppChannel, user.AppID, user.UserID)
	if errCode != ecode.OK {
		utils.Fail(req, errCode)
	}
}
