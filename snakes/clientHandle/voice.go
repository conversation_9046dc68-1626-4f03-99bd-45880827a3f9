package clientHandle

import (
	"github.com/mitchellh/mapstructure"
	"snakes/common/logx"
	"snakes/common/platform"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/model/common/base"
	"snakes/model/common/request"
	"snakes/model/dao"
	"snakes/usermgr"
	"snakes/utils"
	"snakes/voiceroom"
	"time"
)

// OnEnterVoiceRoom 语聊房模式的回调函数
func (h *Handle) OnEnterVoiceRoom(req *base.GameReqMsg) {
	logx.Infof("OnEnterVoiceRoom params:%+v", req)
	if req.User == nil {
		logx.Errorf("OnEnterVoiceRoom no user param userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrParams)
		return
	}

	params := &request.VoiceEnterRoom{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		logx.Errorf("OnEnterVoiceRoom Decode err:%v", err)
		return
	}

	// 添加用户
	user := &usermgr.User{
		UserID:      req.UserID,
		Nickname:    req.User.Nickname,
		Avatar:      req.User.Avatar,
		AppID:       req.AppID,
		AppChannel:  req.AppChannel,
		GameMode:    req.User.GameMode,
		Coin:        req.User.Coin,
		IsVisitor:   req.User.IsVisitor,
		SSToken:     req.User.SSToken,
		ClientIP:    req.User.ClientIP,
		PlatRoomID:  req.PlatRoomID,
		AllProduct:  make(map[constvar.ProductID]bool),
		Role:        req.User.Role,
		ConnSrvID:   req.FromID,
		LastMsgTime: time.Now().Unix(),
	}
	platform.CacheUserInfo(req.UserID, req.User.Coin, req.AppChannel)
	if errCode := user.UpdateBalance(); errCode != ecode.OK {
		logx.Errorf("OnEnterVoiceRoom user.UpdateBalance failed, errCode:%v, user:%+v", errCode, user)
		utils.Fail(req, errCode)
		return
	}
	_ = user.UpdateSkinPackage()

	voiceRoom := voiceroom.GetInstance().GetVoiceRoom(user.AppChannel, user.AppID, params.RoomID)
	if voiceRoom == nil {
		logx.Infof("OnEnterVoiceRoom voiceRoomID:%v not exist", params.RoomID)
		channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
		errCode := voiceroom.GetInstance().CreateVoiceRoom(user, params, &channelCfg.VoiceConfig)
		if errCode != ecode.OK {
			logx.Errorf("OnEnterVoiceRoom CreateVoiceRoom failed, errCode:%v, params:%v", errCode, params)
			utils.Fail(req, errCode)
			return
		}
	} else {
		logx.Infof("OnEnterVoiceRoom voiceRoomID:%v exist, roomID:%v", params.RoomID, voiceRoom.RoomID)
	}

	// 先加入，否则OnMsg处理EnterVoiceRoom消息时，找不到用户
	usermgr.GetInstance().AddUser(user)

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnEnterVoiceRoom OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnVoiceUserSit 玩家请求坐下
func (h *Handle) OnVoiceUserSit(req *base.GameReqMsg) {
	params := &request.VoiceUserSit{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit userID:%v", user.UserID)
		utils.Fail(req, ecode.ErrVisitorLimit)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnVoiceUserStandUp 玩家请求站起
func (h *Handle) OnVoiceUserStandUp(req *base.GameReqMsg) {
	params := &request.VoiceUserStandUp{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit userID:%v", user.UserID)
		utils.Fail(req, ecode.ErrVisitorLimit)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnVoiceChangeCfg 改变配置
func (h *Handle) OnVoiceChangeCfg(req *base.GameReqMsg) {
	params := &request.VoiceChangeRoomCfg{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit userID:%v", user.UserID)
		utils.Fail(req, ecode.ErrVisitorLimit)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnVoiceUserReady 玩家准备
func (h *Handle) OnVoiceUserReady(req *base.GameReqMsg) {
	params := &request.VoiceUserReady{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit userID:%v", user.UserID)
		utils.Fail(req, ecode.ErrVisitorLimit)
		return
	}

	// 更新一下玩家的金币
	user.NoticeUserCoin()

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnVoiceStartGame 开始游戏
func (h *Handle) OnVoiceStartGame(req *base.GameReqMsg) {
	params := &request.VoiceStartGame{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	if user.IsVisitor {
		logx.Errorf("User visitor limit userID:%v", user.UserID)
		utils.Fail(req, ecode.ErrVisitorLimit)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnVoiceChangeRole 玩家变更角色
func (h *Handle) OnVoiceChangeRole(req *base.GameReqMsg) {
	params := &request.VoiceChangeRole{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnVoiceKickOut 管理员踢除玩家
func (h *Handle) OnVoiceKickOut(req *base.GameReqMsg) {
	params := &request.VoiceKickOut{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}

// OnVoiceRoomInfo 获取语聊房信息
func (h *Handle) OnVoiceRoomInfo(req *base.GameReqMsg) {
	params := &request.VoiceRoomInfo{}
	err := mapstructure.Decode(req.Data.Data, params)
	if err != nil {
		return
	}

	user := usermgr.GetInstance().GetUserById(req.AppChannel, req.AppID, req.UserID)
	if user == nil {
		logx.Errorf("GetUser failed userID:%v", req.UserID)
		utils.Fail(req, ecode.ErrNotFoundUser)
		return
	}

	var packMsg = &request.PackMessage{
		MsgID: req.Data.MsgID,
		Data:  req.Data.Data,
		Ext: request.ExtendInfo{
			AppChannel: req.AppChannel,
			AppID:      req.AppID,
			UserID:     user.UserID,
		},
	}
	errCode := voiceroom.GetInstance().OnMsg(user, packMsg)
	if errCode != ecode.OK {
		logx.Errorf("OnMsg failed userID:%v, errCode:%v", user.UserID, errCode)
		utils.Fail(req, errCode)
		return
	}
}
