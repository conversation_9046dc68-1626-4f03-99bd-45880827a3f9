package clientHandle

import (
	"fmt"
	"snakes/common/logx"
	"snakes/invitemgr"
	"snakes/localmgr"
	"snakes/pairmgr"
	"snakes/roommgr"
	"snakes/usermgr"
	"snakes/voiceroom"
)

// CloseRoom 强制关闭房间
func (h *Handle) CloseRoom(roomID int64) error {
	room := roommgr.GetInstance().GetRoom(roomID)
	if room == nil {
		return fmt.Errorf("no find room:%v", roomID)
	}

	room.ForceCloseRoom()
	return nil
}

// OnClose 关闭网络连接
func (h *Handle) OnClose(appChannel string, appID int64, userID string, conSrvID string) {
	user := usermgr.GetInstance().GetUserById(appChannel, appID, userID)
	if user == nil {
		return
	}

	// 连接服务器ID不一样，拒绝离线
	if user.ConnSrvID != conSrvID {
		logx.Infof("OnClose not equal userID:%v, user.ConnSrvID:%v, conSrvID:%v", userID, user.ConnSrvID, conSrvID)
		return
	}
	logx.Infof("OnClose userID:%v, conSrvID:%v offline", userID, conSrvID)

	// 游戏中离线
	local := localmgr.GetInstance().GetLocal(user.AppChannel, user.AppID, user.UserID)
	if local.RoomID > 0 {
		roommgr.GetInstance().UserOffline(local.RoomID, user.AppChannel, user.AppID, user.UserID)
	}

	pairmgr.GetInstance().CancelPair(user)

	invitemgr.GetInstance().UpdateOnlineStatus(user.AppChannel, user.AppID, user.UserID, false)

	// 从语聊房中离开
	voiceroom.GetInstance().UserOffline(user.AppChannel, user.AppID, user.UserID, user.PlatRoomID)

	// 删除缓存玩家
	usermgr.GetInstance().RmvUser(appChannel, appID, userID)
}
