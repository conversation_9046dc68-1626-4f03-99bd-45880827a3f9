package voiceroom

import (
	"context"
	"encoding/json"
	"fmt"
	"snakes/common/logx"
	"snakes/common/redisx"
	"snakes/common/safe"
	"snakes/common/tools"
	"snakes/conf"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/model"
	"snakes/model/common/base"
	"snakes/model/common/request"
	"snakes/model/common/response"
	"snakes/model/dao"
	"snakes/usermgr"
	"sort"
	"sync"
	"time"
)

type VoiceMgr struct {
	sync.Mutex
	allVoiceRoom sync.Map
	stopCh       chan int
	stopWg       sync.WaitGroup
}

var voiceMgr = &VoiceMgr{
	stopCh: make(chan int, 1),
}

func GetInstance() *VoiceMgr {
	return voiceMgr
}

// GetVoiceRoomKey 获取语聊房的key
func (slf *VoiceMgr) GetVoiceRoomKey(appChannel string, appID int64, platRoomID string) string {
	return fmt.Sprintf("%v%v%v", appChannel, appID, platRoomID)
}

// GetVoiceRoom 获取语聊房
func (slf *VoiceMgr) GetVoiceRoom(appChannel string, appID int64, platRoomID string) *VoiceRoom {
	key := slf.GetVoiceRoomKey(appChannel, appID, platRoomID)
	value, ok := slf.allVoiceRoom.Load(key)
	if !ok {
		return nil
	}
	return value.(*VoiceRoom)
}

// CreateVoiceRoom 创建语聊房
func (slf *VoiceMgr) CreateVoiceRoom(user *usermgr.User, params *request.VoiceEnterRoom, voiceConf *conf.VoiceConf) int {
	if len(params.RoomID) == 0 {
		return ecode.ErrParams
	}

	// 校验请求参数
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(user.AppChannel, user.AppID)
	roomConf := channelCfg.GetRoomConf(constvar.RoomTypeVoice)
	if roomConf == nil {
		return ecode.ErrRoomConfig
	}
	if !tools.IsContain[int](roomConf.Fees, voiceConf.Fee) ||
		!tools.IsContain[int](roomConf.PlayerNums, voiceConf.PlayerNum) ||
		!tools.IsContain[int](roomConf.GridNums, voiceConf.GridNum) {
		return ecode.ErrRoomConfig
	}

	voiceRoom := slf.GetVoiceRoom(user.AppChannel, user.AppID, params.RoomID)
	if voiceRoom == nil {
		voiceRoomKey := slf.GetVoiceRoomKey(user.AppChannel, user.AppID, params.RoomID)
		slf.allVoiceRoom.Store(voiceRoomKey, &VoiceRoom{
			AppChannel:     user.AppChannel,
			AppID:          user.AppID,
			PlatRoomID:     params.RoomID,
			RoomID:         0, // 刚创建的语聊房还没有相关的游戏房间
			PlayerNum:      voiceConf.PlayerNum,
			Fee:            voiceConf.Fee,
			GridNum:        voiceConf.GridNum,
			PropMode:       voiceConf.PropMode,
			KickOut:        voiceConf.KickOut,
			CreateTime:     time.Now(),
			stopCh:         make(chan int, 1),
			lastActiveTime: time.Now(),
			lastMsgTime:    time.Now(),
			msgChan:        make(chan *request.PackMessage, 2000),
		})
		_ = dao.GroupDao.VoiceInfo.Set(user.AppChannel, user.AppID, params.RoomID, &dao.VoiceInfo{SrvID: conf.Conf.Server.ID, FreshTime: time.Now().Unix()})
		voiceRoom = slf.GetVoiceRoom(user.AppChannel, user.AppID, params.RoomID)
		voiceRoom.Start()
		// 创建语聊房(拉起游戏)，上报初始语聊房配置
		voiceRoom.ReportConfig()
		logx.Infof("VoiceMgr CreateVoiceRoom success platRoomID:%v, voiceConf:%+v", params.RoomID, voiceConf)
	}
	return ecode.OK
}

// Start 启动 管理器的主动逻辑,主要实现语聊房的释放
func (slf *VoiceMgr) Start() {
	slf.stopWg.Add(1)
	safe.Go(func() {
		ticker := time.NewTicker(time.Second)
		defer func() {
			logx.Info("VoiceMgr:Start 工作协程退出")
			slf.removeAll()
			ticker.Stop()
			slf.stopWg.Done()
		}()

		var count int
		for {
			select {
			case <-slf.stopCh:
				return
			case <-ticker.C:
				count++

				// 每分钟打印一次所有房间
				if count%60 == 0 {
					slf.PrintRooms()
					slf.CheckSrvStop()
				}

				// 每2分钟检测一次所有房间
				if count%120 == 0 {
					slf.CheckRooms()
				}
			}
		}
	})
}

// Stop 退出
func (slf *VoiceMgr) Stop() {
	close(slf.stopCh)
	slf.stopWg.Wait()
}

// UserOffline 玩家离线
func (slf *VoiceMgr) UserOffline(appChannel string, appID int64, userID string, platRoomID string) {
	voiceRoom := slf.GetVoiceRoom(appChannel, appID, platRoomID)
	if voiceRoom == nil {
		return
	}

	voiceRoom.OnMsg(&request.PackMessage{
		MsgID: constvar.MsgTypeUserOffline,
		Ext: request.ExtendInfo{
			AppChannel: appChannel,
			AppID:      appID,
			UserID:     userID,
		},
	})
}

// GetRoomCount 获取房间数量
func (slf *VoiceMgr) GetRoomCount() int {
	var count int
	slf.allVoiceRoom.Range(func(key, value any) bool {
		count++
		return true
	})
	return count
}

// CheckRooms 检查所有房间
func (slf *VoiceMgr) CheckRooms() {
	var nowTime = time.Now()
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if nowTime.Sub(voiceRoom.lastActiveTime) > time.Minute*2 || nowTime.Sub(voiceRoom.lastMsgTime) > time.Hour*6 {
			logx.Infof("VoiceMgr Delete Expire platRoomID:%v success, lastActiveTime:%v, lastMsgTime:%v", voiceRoom.PlatRoomID, voiceRoom.lastActiveTime, voiceRoom.lastMsgTime)
			voiceRoom.Stop()
			slf.allVoiceRoom.Delete(key)
			dao.GroupDao.VoiceInfo.Delete(voiceRoom.AppChannel, voiceRoom.AppID, voiceRoom.PlatRoomID)
		}
		return true
	})
}

// PrintRooms 打印所有房间
func (slf *VoiceMgr) PrintRooms() {
	logx.Infof("===VoiceMgr PrintRooms roomCount:%v", slf.GetRoomCount())
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if voiceRoom != nil {
			playerCount, robotCount, viewerCount := voiceRoom.GetPlayerCount()
			logx.Infof("===VoiceMgr PlatRoomID:%v, playerCount:%v, robotCount:%v, viewerCount:%v", voiceRoom.PlatRoomID, playerCount, robotCount, viewerCount)
		}
		return true
	})
}

func (slf *VoiceMgr) OnMsg(user *usermgr.User, msg *request.PackMessage) int {
	voiceRoom := slf.GetVoiceRoom(user.AppChannel, user.AppID, user.PlatRoomID)
	if voiceRoom == nil {
		logx.Infof("VoiceMgr OnMsg no find PlatRoomID:%v", user.PlatRoomID)
		return ecode.ErrNotFoundVoiceRoom
	}

	voiceRoom.OnMsg(msg)
	return ecode.OK
}

func (slf *VoiceMgr) QueryRoomList(appChannel string, appID int64) []*response.QueryVoiceStatus {
	var roomList = make([]*response.QueryVoiceStatus, 0)
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if voiceRoom.AppChannel != appChannel ||
			voiceRoom.AppID != appID {
			return true
		}

		// 跳过 没有管理员的、游戏已经开始的、座位上人满的
		if voiceRoom.GetAdmin() == nil ||
			voiceRoom.RoomID > 0 ||
			voiceRoom.GetFreePos() < 0 {
			return true
		}

		playerNum, ids := voiceRoom.GetSitUserIds()
		room := &response.QueryVoiceStatus{
			PlatRoomId:   voiceRoom.PlatRoomID,
			GameId:       conf.Conf.Server.GameId,
			MaxUserCount: playerNum,
			CurUserCount: len(ids),
			StartTime:    voiceRoom.CreateTime.UnixMilli(),
			Users:        ids,
		}
		room.Attendance = float64(room.CurUserCount) / float64(room.MaxUserCount)

		roomList = append(roomList, room)
		return true
	})

	// 房间排序后，最多返回100个
	sort.Slice(roomList, func(i, j int) bool {
		return roomList[i].GetSortScore() > roomList[j].GetSortScore()
	})
	if len(roomList) > 100 {
		roomList = roomList[:100]
	}

	return roomList
}

// removeAll 删除所有语聊房
func (slf *VoiceMgr) removeAll() {
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		voiceRoom.Stop()
		slf.allVoiceRoom.Delete(key)
		dao.GroupDao.VoiceInfo.Delete(voiceRoom.AppChannel, voiceRoom.AppID, voiceRoom.PlatRoomID)
		return true
	})
}

// CheckSrvStop 检查本服务器是否已关停
func (slf *VoiceMgr) CheckSrvStop() {
	slf.Lock()
	defer slf.Unlock()

	service, err := model.NewGameServiceSearch().SetGameID(conf.Conf.Server.GameId).SetSrvID(conf.Conf.Server.ID).First()
	if err != nil {
		logx.Errorf("CheckSrvStop getService err:%v", err)
		return
	}

	if !service.IsStop {
		return
	}

	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if voiceRoom.RoomID > 0 {
			return true
		}

		voiceRoom.Stop()
		slf.allVoiceRoom.Delete(key)
		dao.GroupDao.VoiceInfo.Delete(voiceRoom.AppChannel, voiceRoom.AppID, voiceRoom.PlatRoomID)
		// 通知连接服务器，强制关闭语聊房
		bytes, _ := json.Marshal(&base.VoiceShutdown{
			AppChannel: voiceRoom.AppChannel,
			AppID:      voiceRoom.AppID,
			PlatRoomID: voiceRoom.PlatRoomID,
		})
		redisx.GetClient().Publish(context.TODO(), dao.VoiceShutdownKey(), string(bytes))
		logx.Infof("VoiceMgr CheckSrvStop forceClose platRoomID:%v success", voiceRoom.PlatRoomID)
		return true
	})
}

// GetPlayerCount 获取 所有房间的玩家数量和观众数量
func (slf *VoiceMgr) GetPlayerCount() (int, int, int) {
	var userCount, robotCount, viewerCount int
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if voiceRoom != nil {
			uc, rc, vc := voiceRoom.GetPlayerCount()
			userCount += uc
			robotCount += rc
			viewerCount += vc
		}
		return true
	})
	return userCount, robotCount, viewerCount
}

// CreateGame 创建游戏
func (slf *VoiceMgr) CreateGame(params *request.CreateGame) (*response.CreateGame, int) {
	voiceRoom := slf.GetVoiceRoom(params.AppChannel, params.AppId, params.PlatRoomId)
	if voiceRoom != nil {
		if voiceRoom.RoomID > 0 {
			logx.Infof("CreateGame voiceRoom playing, platRoomId:%v, roomID:%v", params.PlatRoomId, voiceRoom.RoomID)
			return nil, ecode.ErrGameExist
		}

		isSame := voiceRoom.IsSameParam(params)
		logx.Infof("CreateGame IsSameParam isSame:%v", isSame)
		if !isSame {
			logx.Infof("CreateGame voiceRoom exist, platRoomId:%v", params.PlatRoomId)
			return nil, ecode.ErrGameExist
		}
		voiceRoom.PlayerNum = len(params.Users) // 重置玩家人数，因为玩家人数可能改变了
		voiceRoom.PropMode = params.ApiProp.PropMode()
	}

	if voiceRoom == nil {
		voiceConf := &conf.VoiceConf{PlayerNum: len(params.Users), Fee: params.Bet, GridNum: params.GridNum, PropMode: params.ApiProp.PropMode(), KickOut: true}
		voiceRoomKey := slf.GetVoiceRoomKey(params.AppChannel, params.AppId, params.PlatRoomId)
		slf.allVoiceRoom.Store(voiceRoomKey, &VoiceRoom{
			AppChannel:     params.AppChannel,
			AppID:          params.AppId,
			PlatRoomID:     params.PlatRoomId,
			RoomID:         0, // 刚创建的语聊房还没有相关的游戏房间
			PlayerNum:      voiceConf.PlayerNum,
			Fee:            voiceConf.Fee,
			GridNum:        voiceConf.GridNum,
			PropMode:       voiceConf.PropMode,
			KickOut:        voiceConf.KickOut,
			CreateTime:     time.Now(),
			stopCh:         make(chan int, 1),
			lastActiveTime: time.Now(),
			lastMsgTime:    time.Now(),
			msgChan:        make(chan *request.PackMessage, 2000),
		})
		_ = dao.GroupDao.VoiceInfo.Set(params.AppChannel, params.AppId, params.PlatRoomId, &dao.VoiceInfo{SrvID: conf.Conf.Server.ID, FreshTime: time.Now().Unix()})
		voiceRoom = slf.GetVoiceRoom(params.AppChannel, params.AppId, params.PlatRoomId)
		voiceRoom.Start()
	}

	return voiceRoom.CreateGame(params)
}

// CloseVoiceRoom 关闭语聊房
func (slf *VoiceMgr) CloseVoiceRoom(appChannel string, appID int64, platRoomID string) {
	slf.allVoiceRoom.Range(func(key, value any) bool {
		voiceRoom := value.(*VoiceRoom)
		if voiceRoom.AppChannel == appChannel && voiceRoom.AppID == appID && voiceRoom.PlatRoomID == platRoomID {
			logx.Infof("VoiceMgr Close appChannel:%v, appID:%v, platRoomID:%v success", appChannel, appID, platRoomID)
			voiceRoom.Stop()
			slf.allVoiceRoom.Delete(key)
			dao.GroupDao.VoiceInfo.Delete(voiceRoom.AppChannel, voiceRoom.AppID, voiceRoom.PlatRoomID)
		}
		return true
	})
}
