package persistmgr

import (
	"snakes/common/safe"
	"sync"
)

type PersistMgr struct {
	sync.WaitGroup
	*GameRecordPersist
	*UserSkinPersist
}

func NewPersistMgr() *PersistMgr {
	return &PersistMgr{
		GameRecordPersist: NewGameRecordPersist(),
		UserSkinPersist:   NewUserSkinPersist(),
	}
}

func (slf *PersistMgr) Start() {
	// 仅主服务做持久化工作
	slf.GameRecordPersist.Start()
	slf.UserSkinPersist.Start()
}

func (slf *PersistMgr) Stop() {
	slf.Add(2)
	safe.Go(func() {
		slf.GameRecordPersist.Stop()
		slf.Done()
	})
	safe.Go(func() {
		slf.UserSkinPersist.Stop()
		slf.Done()
	})
}
