package request

import "snakes/constvar"

// VoiceEnterRoom 进入语聊房
type VoiceEnterRoom struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// VoiceUserSit 玩家请求坐下
type VoiceUserSit struct {
	RoomID string `json:"roomId"` // app的语聊房Id
	Pos    int    `json:"pos"`    // 请求坐下的游戏位置
}

// VoiceUserStandUp 玩家请求站起
type VoiceUserStandUp struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// VoiceChangeRoomCfg 改变语聊房的配置
type VoiceChangeRoomCfg struct {
	RoomID    string `json:"roomId"`    // app的语聊房Id
	PlayerNum int    `json:"playerNum"` // 玩家人数
	Fee       int    `json:"fee"`       // 房间费
	GridNum   int    `json:"gridNum"`   // 方格数
	PropMode  int    `json:"propMode"`  // 0 无道具模式 1 有道具模式
}

// VoiceUserReady 玩家准备
type VoiceUserReady struct {
	RoomID string `json:"roomId"` // app的语聊房Id
	Ready  bool   `json:"ready"`  // 是否准备
}

// VoiceStartGame 开始游戏
type VoiceStartGame struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// VoiceChangeRole 改变角色
type VoiceChangeRole struct {
	Role constvar.Role `json:"role"` // 要变更的新角色类型
}

// VoiceKickOut 踢人
type VoiceKickOut struct {
	RoomID string `json:"roomId"` // app的语聊房Id
	UserID string `json:"userId"` // 玩家ID
}

// VoiceRoomInfo 获取语聊房信息
type VoiceRoomInfo struct {
	RoomID string `json:"roomId"` // app的语聊房Id
}

// QueryVoiceRoom 渠道app查询语聊房列表
type QueryVoiceRoom struct {
	AppChannel string `json:"app_channel"` // 渠道名称
	AppId      int64  `json:"app_id"`      // AppId
}

// CreateGame 渠道app服务端匹配好玩家直接创建游戏
type (
	CreateGame struct {
		AppChannel string            `json:"app_channel"`
		AppId      int64             `json:"app_id"`
		PlatRoomId string            `json:"plat_room_id"` // 平台的房间Id
		GameId     int               `json:"game_id"`      // 游戏ID
		GridNum    int               `json:"grid_num"`     // 方格数
		Users      []*UserInfo       `json:"users"`        // 玩家列表
		Extend     string            `json:"extend"`       // 扩展参数
		ApiScene   constvar.ApiScene `json:"api_scene"`    // api 调用场景 1-快速开始模式 2-PK模式
		Bet        int               `json:"bet"`          // 入场费
		ApiProp    constvar.ApiProp  `json:"api_prop"`     // api 道具模式
	}

	UserInfo struct {
		UserId   string        `json:"user_id"`   // 玩家Id
		NickName string        `json:"nick_name"` // 玩家昵称
		Avatar   string        `json:"avatar"`    // 玩家头像地址
		Role     constvar.Role `json:"role"`      // 玩家角色
		Token    string        `json:"token"`     // token
		IsAI     int           `json:"is_ai"`     // 是否是ai
	}

	CloseGame struct {
		AppChannel string `json:"app_channel"`
		AppId      int64  `json:"app_id"`
		PlatRoomId string `json:"plat_room_id"` // 平台的房间Id
		GameId     int    `json:"game_id"`      // 游戏ID
	}

	UserLeave struct {
		AppChannel string `json:"app_channel"`
		AppId      int64  `json:"app_id"`
		PlatRoomId string `json:"plat_room_id"` // 平台的房间Id
		GameId     int    `json:"game_id"`      // 游戏ID
		UserId     string `json:"user_id"`      // 玩家Id
	}
)
