package base

import (
	"snakes/common/msg"
	"snakes/constvar"
)

// FirstMove 用户先手数据
type FirstMove struct {
	UserID     string `json:"userId"`     // 用户ID
	DicePoint  int    `json:"dicePoint"`  // 点数
	Rank       int    `json:"rank"`       // 点数排名
	IsRollDice bool   `json:"isRollDice"` // 本轮先手是否参与掷骰子
}

type GameUser struct {
	Nickname  string        `json:"nickname"`  // 昵称
	Avatar    string        `json:"avatar"`    // 头像
	Coin      int64         `json:"coin"`      // 当前金币
	IsVisitor bool          `json:"isVisitor"` // 是否是游客
	SSToken   string        `json:"SSToken"`   // platform登录成功返回的Token
	GameMode  int           `json:"gameMode"`  // 游戏模式 2-半屏 3-全屏
	ClientIP  string        `json:"clientIP"`  // 玩家的IP地址
	Role      constvar.Role `json:"role"`      // 玩家角色 0-普通玩家 1-游客 2-管理员
}

// GameReqMsg 游戏中请求消息
type GameReqMsg struct {
	AppChannel string            `json:"appChannel"` // 平台渠道
	AppID      int64             `json:"appId"`      // appID
	UserID     string            `json:"userId"`     // 玩家ID
	RoomID     int64             `json:"roomId"`     // 游戏房间ID
	PlatRoomID string            `json:"platRoomId"` // 语聊房ID
	FromID     string            `json:"fromId"`     // 来源服务器ID
	User       *GameUser         `json:"user"`       // 请求匹配、创建邀请、接受邀请、进入语聊房消息，需携带用户信息
	Data       msg.FromClientMsg `json:"data"`       // 消息体
}

// GameAckMsg 游戏中响应消息
type GameAckMsg struct {
	AppChannel string           `json:"appChannel"` // 平台渠道
	AppID      int64            `json:"appId"`      // appID
	UserID     string           `json:"userId"`     // 玩家ID(广播时，用户ID为空)
	PlatRoomID string           `json:"platRoomId"` // 语聊房ID(带这个值表示广播)
	ExpectID   string           `json:"expectId"`   // 除了某人
	Data       *msg.ToClientMsg `json:"data"`       // 消息体
}
