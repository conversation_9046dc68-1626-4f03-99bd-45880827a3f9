package model

import (
	"fmt"
	"go.uber.org/atomic"
	"gorm.io/gorm"
	"snakes/common/gormx"
)

type (
	GameService struct {
		ID             uint         `gorm:"column:Id;primary_key"`
		GameID         int          `gorm:"column:GameID;type:int;default:0;index:idx_game"`   // 游戏ID
		SrvID          string       `gorm:"column:SrvID;type:varchar(50);default:''"`          // 游戏服务器ID
		HttpDomain     string       `gorm:"column:HttpDomain;type:varchar(50);default:''"`     // http域名
		HttpAddr       string       `gorm:"column:HttpAddr;type:varchar(50);default:''"`       // http地址
		HttpDetectAddr string       `gorm:"column:HttpDetectAddr;type:varchar(50);default:''"` // http探测路径
		IsStop         bool         `gorm:"column:IsStop;type:tinyint(1);default:0"`           // 是否停用
		LimitCount     int          `gorm:"column:LimitCount;type:int;default:0"`              // 限制数量
		Sort           int          `gorm:"column:Sort;type:int;default:0"`                    // 排序(优先级)
		FailTimes      atomic.Int64 `gorm:"-"`                                                 // 接口请求连续失败次数
		TryTime        atomic.Int64 `gorm:"-"`                                                 // 最近一次接口请求时间
	}

	GameServiceSearch struct {
		Orm *gorm.DB
		GameService
		Page
		Order string
	}
)

// TableName 指定表名.
func (*GameService) TableName() string {
	return "game_service"
}

func NewGameServiceSearch() *GameServiceSearch {
	return &GameServiceSearch{Orm: gormx.GetDB()}
}

func (slf *GameServiceSearch) SetOrm(tx *gorm.DB) *GameServiceSearch {
	slf.Orm = tx
	return slf
}

func (slf *GameServiceSearch) SetPage(page, size int) *GameServiceSearch {
	slf.PageNum, slf.PageSize = page, size
	return slf
}

func (slf *GameServiceSearch) SetOrder(order string) *GameServiceSearch {
	slf.Order = order
	return slf
}

func (slf *GameServiceSearch) SetGameID(gameID int) *GameServiceSearch {
	slf.GameID = gameID
	return slf
}

func (slf *GameServiceSearch) SetSrvID(srvID string) *GameServiceSearch {
	slf.SrvID = srvID
	return slf
}

// Create 单条插入
func (slf *GameServiceSearch) Create(recordM *GameService) (uint, error) {
	if err := slf.Orm.Create(recordM).Error; err != nil {
		return 0, fmt.Errorf("create recordM err: %v, recordM: %+v", err, recordM)
	}

	return recordM.ID, nil
}

// CreateBatch 批量插入
func (slf *GameServiceSearch) CreateBatch(records []*GameService) error {
	if err := slf.Orm.Create(&records).Error; err != nil {
		return fmt.Errorf("create records err: %v, records: %+v", err, records)
	}

	return nil
}

// build 构建条件.
func (slf *GameServiceSearch) build() *gorm.DB {
	var db = slf.Orm.Table(slf.TableName()).Model(GameService{})

	if slf.GameID > 0 {
		db = db.Where("GameID = ?", slf.GameID)
	}

	if len(slf.SrvID) > 0 {
		db = db.Where("SrvID = ?", slf.SrvID)
	}

	if slf.Order != "" {
		db = db.Order(slf.Order)
	}

	return db
}

// Find 多条查询.
func (slf *GameServiceSearch) Find() ([]*GameService, int64, error) {
	var (
		records = make([]*GameService, 0)
		total   int64
		db      = slf.build()
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find records err: %v", err)
	}

	return records, total, nil
}

// FindNotTotal 多条查询.
func (slf *GameServiceSearch) FindNotTotal() ([]*GameService, error) {
	var (
		records = make([]*GameService, 0)
		db      = slf.build()
	)

	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, fmt.Errorf("find records err: %v", err)
	}

	return records, nil
}

// FindByQuery 指定条件查询.
func (slf *GameServiceSearch) FindByQuery(query string, args []interface{}) ([]*GameService, int64, error) {
	var (
		records = make([]*GameService, 0)
		total   int64
		db      = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if err := db.Count(&total).Error; err != nil {
		return records, total, fmt.Errorf("find by query count err: %v", err)
	}
	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, total, fmt.Errorf("find by query records err: %v, query: %s, args: %+v", err, query, args)
	}

	return records, total, nil
}

// FindByQueryNotTotal 指定条件查询&不查询总条数.
func (slf *GameServiceSearch) FindByQueryNotTotal(query string, args []interface{}) ([]*GameService, error) {
	var (
		records = make([]*GameService, 0)
		db      = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if slf.PageNum*slf.PageSize > 0 {
		db = db.Offset((slf.PageNum - 1) * slf.PageSize).Limit(slf.PageSize)
	}
	if err := db.Find(&records).Error; err != nil {
		return records, fmt.Errorf("find by query records err: %v, query: %s, args: %+v", err, query, args)
	}

	return records, nil
}

// First 单条查询.
func (slf *GameServiceSearch) First() (*GameService, error) {
	var (
		recordM = new(GameService)
		db      = slf.build()
	)

	if err := db.First(recordM).Error; err != nil {
		return recordM, err
	}

	return recordM, nil
}

// UpdateByMap 更新.
func (slf *GameServiceSearch) UpdateByMap(upMap map[string]interface{}) error {
	var (
		db = slf.build()
	)

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by map err: %v, upMap: %+v", err, upMap)
	}

	return nil
}

// UpdateByQuery 指定条件更新.
func (slf *GameServiceSearch) UpdateByQuery(query string, args []interface{}, upMap map[string]interface{}) error {
	var (
		db = slf.Orm.Table(slf.TableName()).Where(query, args...)
	)

	if err := db.Updates(upMap).Error; err != nil {
		return fmt.Errorf("update by query err: %v, query: %s, args: %+v, upMap: %+v", err, query, args, upMap)
	}

	return nil
}
