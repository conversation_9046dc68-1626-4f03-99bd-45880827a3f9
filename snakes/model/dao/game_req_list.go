package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"snakes/common/logx"
	"snakes/common/redisx"
	"snakes/conf"
	"snakes/model/common/base"
	"time"
)

type GameReqList struct{}

func (u *GameReqList) Key(toID string) string {
	key := fmt.Sprintf("%v:gameReqList:%v", conf.Conf.Server.Project, toID)
	return key
}

func (u *GameReqList) Add(toID string, data *base.GameReqMsg) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		logx.Errorf("Marshal err:%v", err)
		return err
	}
	_, err = redisx.GetClient().RPush(context.TODO(), u.Key(toID), string(bytes)).Result()
	return err
}

func (u *GameReqList) BLPop() ([]string, error) {
	return redisx.GetClient().BLPop(context.TODO(), time.Second, u.Key(conf.Conf.Server.ID)).Result()
}

func (u *GameReqList) Delete() {
	_, _ = redisx.GetClient().Del(context.TODO(), u.Key(conf.Conf.Server.ID)).Result()
}
