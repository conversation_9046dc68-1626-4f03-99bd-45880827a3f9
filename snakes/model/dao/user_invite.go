package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"snakes/common/redisx"
	"snakes/conf"
	"time"
)

type UserInvite struct {
	SrvID      string // 游戏服务器ID
	InviteCode int    // 邀请码
	FreshTime  int
}

func (c *UserInvite) Key(appChannel string, appID int64, userID string) string {
	key := fmt.Sprintf("%v:userInvite:%v:%v:%v", conf.Conf.Server.Project, appChannel, appID, userID)
	return key
}

func (c *UserInvite) Set(appChannel string, appID int64, userID string, info *UserInvite) error {
	dataBytes, err := json.Marshal(info)
	if err != nil {
		return err
	}

	_, err = redisx.GetClient().Set(context.TODO(), c.Key(appChannel, appID, userID), string(dataBytes), time.Hour*24*7).Result()
	return err
}

func (c *UserInvite) Get(appChannel string, appID int64, userID string) (*UserInvite, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(appChannel, appID, userID)).Result()
	if err != nil {
		return &UserInvite{}, err
	}

	var data = new(UserInvite)
	err = json.Unmarshal([]byte(value), data)
	return data, err
}

func (c *UserInvite) Delete(appChannel string, appID int64, userID string) {
	_, _ = redisx.GetClient().Del(context.TODO(), c.Key(appChannel, appID, userID)).Result()
}
