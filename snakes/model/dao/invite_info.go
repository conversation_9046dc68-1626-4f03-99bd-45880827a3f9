package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"snakes/common/redisx"
	"snakes/conf"
	"time"
)

type InviteInfo struct {
	SrvID     string // 游戏服务ID
	FreshTime int64  // 刷新时间戳，秒(判断这个邀请是否还存活)
}

func (c *InviteInfo) Key(inviteCode int) string {
	key := fmt.Sprintf("%v:inviteInfo:%v", conf.Conf.Server.Project, inviteCode)
	return key
}

func (c *InviteInfo) Set(inviteCode int, info *InviteInfo) error {
	dataBytes, err := json.Marshal(info)
	if err != nil {
		return err
	}

	_, err = redisx.GetClient().Set(context.TODO(), c.Key(inviteCode), string(dataBytes), time.Hour*24*7).Result()
	return err
}

func (c *InviteInfo) Get(inviteCode int) (*InviteInfo, error) {
	value, err := redisx.GetClient().Get(context.TODO(), c.Key(inviteCode)).Result()
	if err != nil {
		return &InviteInfo{}, err
	}

	var data = new(InviteInfo)
	err = json.Unmarshal([]byte(value), data)
	return data, err
}

func (c *InviteInfo) Delete(inviteCode int) {
	_, _ = redisx.GetClient().Del(context.TODO(), c.Key(inviteCode)).Result()
}
