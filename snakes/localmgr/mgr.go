package localmgr

import (
	"fmt"
	"snakes/common/logx"
	"snakes/conf"
	"snakes/model/dao"
	"sync"
)

type (
	Local struct {
		RoomID    int64 // 房间ID
		FreshTime int64 // 刷新时间戳，秒
	}

	LocalManager struct {
		localMap sync.Map
	}
)

var instance = new(LocalManager)

func GetInstance() *LocalManager {
	return instance
}

func (u *LocalManager) SetLocal(appChannel string, appID int64, userID string, local *Local) {
	var key = fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	u.localMap.Store(key, local)
	logx.Infof("SetLocal userID:%v, local:%+v", userID, local)
	_ = dao.GroupDao.UserLocation.Set(appChannel, appID, userID, &dao.UserLocation{
		SrvID:     conf.Conf.Server.ID,
		RoomID:    local.RoomID,
		FreshTime: local.FreshTime,
	})
}

func (u *LocalManager) GetLocal(appChannel string, appID int64, userID string) *Local {
	var key = fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	value, ok := u.localMap.Load(key)
	if ok {
		return value.(*Local)
	}
	return &Local{}
}

func (u *LocalManager) RmvLocal(appChannel string, appID int64, userID string) {
	local := u.GetLocal(appChannel, appID, userID)
	key := fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	u.localMap.Delete(key)
	dao.GroupDao.UserLocation.Delete(appChannel, appID, userID)
	logx.Infof("RmvLocal userID:%v, roomID:%v", userID, local.RoomID)
}

func (u *LocalManager) RmvLocalByRoomID(appChannel string, appID int64, userID string, roomID int64) {
	local := u.GetLocal(appChannel, appID, userID)
	if local.RoomID != roomID {
		return
	}

	var key = fmt.Sprintf("%v_%v_%v", appChannel, appID, userID)
	u.localMap.Delete(key)
	dao.GroupDao.UserLocation.Delete(appChannel, appID, userID)
	logx.Infof("RmvLocalByRoomID userID:%v, roomID:%v", userID, local.RoomID)
}
