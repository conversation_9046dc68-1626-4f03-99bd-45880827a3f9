#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/snakes_live/snakes_live
rm -rf snakes
mv snakes_latest snakes
chmod +x snakes
if supervisord ctl status | awk '{print $1}' | grep  "snakes_live$"; then
    supervisord ctl restart snakes_live
else
    supervisord ctl reload
    supervisord ctl start snakes_live
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o snakes_latest
ssh root@************* "mkdir -p /data/soofun_game/snakes_live/snakes_live"
scp ./snakes_latest root@*************:/data/soofun_game/snakes_live/snakes_live/
rm -rf ./snakes_latest
ssh root@************* "[ ! -e /data/soofun_game/snakes_live/snakes_live/config ]" && scp -r ./config root@*************:/data/soofun_game/snakes_live/snakes_live/
ssh root@************* "[ ! -e /etc/supervisor/conf.d/snakes_live.conf ]" && scp ./cicd/baiyou_test/supervisor/snakes_live.conf root@*************:/etc/supervisor/conf.d/snakes_live.conf
ssh root@************* bash <<eof
$SHELL1
eof