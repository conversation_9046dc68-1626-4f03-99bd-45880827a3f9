#!/usr/bin/env bash
SHELL1=$(cat <<'eof'
hostname
set -v

cd /data/soofun_game/snakes/snakes
rm -rf snakes
mv snakes_latest snakes
chmod +x snakes
if supervisord ctl status | awk '{print $1}' | grep  "snakes$"; then
    supervisord ctl restart snakes
else
    supervisord ctl reload
    supervisord ctl start snakes
fi
eof
)

go mod tidy
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o snakes_latest
ssh root@************* "mkdir -p /data/soofun_game/snakes/snakes"
scp ./snakes_latest root@*************:/data/soofun_game/snakes/snakes/
rm -rf ./snakes_latest
ssh root@************* "[ ! -e /data/soofun_game/snakes/snakes/config ]" && scp -r ./config root@*************:/data/soofun_game/snakes/snakes/
ssh root@************* "[ ! -e /etc/supervisor/conf.d/snakes.conf ]" && scp ./cicd/baiyou_test/supervisor/snakes.conf root@*************:/etc/supervisor/conf.d/snakes.conf
ssh root@************* bash <<eof
$SHELL1
eof
