package roommgr

import (
	"snakes/constvar"
)

// RoomUser 玩家用户信息
type RoomUser struct {
	UserID       string                // 玩家ID(即平台玩家ID)
	NickName     string                // 昵称
	Avatar       string                // 头像
	Coin         int64                 // 当前金币
	SSToken      string                // platform登录成功返回的Token
	ClientIp     string                // 玩家的IP地址
	PlatRoomID   string                // 客户端传递过来的平台RoomID
	IdentityType constvar.IdentityType // 身份类型 1-玩家 2-机器人
	RobotLevel   constvar.RobotLevel   // 机器人等级
	UserStatus   constvar.UserStatus   // 用户状态
	IsOffline    bool                  // 是否已断线
	IsLeave      bool                  // 是否已离开
	SkinChessID  constvar.ProductID    // 棋子的皮肤Id

	Pos         int   // 座位号
	LastMsgTime int64 // 该玩家最近一条操作的时间戳
}

func (u *RoomUser) IsRobot() bool {
	if u.IdentityType == constvar.IdentityTypeRobot {
		return true
	}
	return false
}

func (u *RoomUser) isOffline() bool {
	if u.IsOffline || u.IsLeave {
		return true
	}
	return false
}

func (u *RoomUser) setRobotLevel(level constvar.RobotLevel) bool {
	if !u.IsRobot() {
		return false
	}
	if !level.Valid() {
		level = constvar.RobotLevelEasy
	}
	u.RobotLevel = level
	return true
}
