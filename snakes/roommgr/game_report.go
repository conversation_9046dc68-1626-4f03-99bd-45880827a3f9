package roommgr

import (
	"encoding/json"
	"ms-version.soofun.online/wjl/game_public/external"
	"ms-version.soofun.online/wjl/game_public/types_public"
	"snakes/common/convertx"
	"snakes/common/logx"
	"snakes/common/platform"
	"snakes/common/safe"
	"snakes/common/tools"
	"snakes/conf"
	"snakes/constvar"
	"snakes/model/common/base"
)

// ReportGameStart 上报游戏开始
func (slf *Room) ReportGameStart() {
	safe.Go(func() {
		var reportUser *RoomUser
		allUser := slf.GetAllUser()
		for _, v := range allUser {
			if v.IsRobot() || len(v.SSToken) == 0 {
				continue
			}

			if reportUser == nil {
				reportUser = v
			}
		}
		if reportUser == nil {
			logx.Errorf("RoomID:%v ReportGameStart no find reportUser", slf.RoomID)
			return
		}

		var players = make([]external.PlayerStartInfo, 0)
		for _, v := range allUser {
			if v.UserStatus != constvar.UserStatusSit {
				continue
			}

			players = append(players, external.PlayerStartInfo{
				UserID: v.UserID,
				IsAI:   convertx.BoolToInt(v.IsRobot()), // 1 机器人 ,0 真人
				Bet:    slf.fee,
			})
		}

		extendBytes, _ := json.Marshal(&base.ReportExtend{
			Extend: slf.apiCreateExtend,
			Users:  make([]*base.ReportUser, 0),
		})
		reportData := &external.GameStartInfo{
			GameID:       conf.Conf.Server.GameId,
			RoomID:       slf.voiceRoom.GetVoiceRoomId(),
			GameRoundID:  slf.GetRoundID(),
			CurrencyType: 0,
			StartAt:      slf.CreateTime.UnixMilli(),
			Players:      players,
			Extend:       string(extendBytes),
		}

		resp, err := platform.ZegoGame().ReportGameStart(types_public.BobiEnv(conf.Conf.Server.Env), slf.appID, reportUser.UserID, reportUser.SSToken, reportData)
		logx.Infof("RoomID:%v ReportGameStart reportData:%v, resp:%v, err:%v", slf.RoomID, tools.GetObj(reportData), tools.GetObj(resp), err)
	})
}

// ReportGameSettle 上报游戏结束
func (slf *Room) ReportGameSettle(start, end int64, roundID string, endInfoList []base.GameEndInfo, extend *base.ReportExtend) {
	go func(start, end int64, roundID string, gameEndInfo []base.GameEndInfo) {
		defer safe.RecoverPanic()
		var reportUser *base.GameEndInfo
		for _, v := range gameEndInfo {
			if v.IsAI == 1 || len(v.SSToken) == 0 {
				continue
			}

			if reportUser == nil {
				reportUser = &v
			}
		}
		if reportUser == nil {
			logx.Errorf("RoomID:%v ReportGameSettle no find reportUser", slf.RoomID)
			return
		}

		var players = make([]external.PlayerResultInfo, 0)
		for _, v := range gameEndInfo {
			players = append(players, external.PlayerResultInfo{
				UserID: v.UserID,
				IsAI:   v.IsAI,
				Rank:   v.Rank,
				Reward: v.Reward,
				Score:  v.Score,
			})
		}

		extendBytes, _ := json.Marshal(extend)
		reportData := &external.GameSettleInfo{
			GameID:       conf.Conf.Server.GameId,
			RoomID:       slf.voiceRoom.GetVoiceRoomId(),
			GameRoundID:  slf.GetRoundID(),
			CurrencyType: 0,
			StartAt:      slf.CreateTime.UnixMilli(),
			EndAt:        end,
			Players:      players,
			Extend:       string(extendBytes),
		}

		resp, err := platform.ZegoGame().ReportGameSettle(types_public.BobiEnv(conf.Conf.Server.Env), slf.appID, reportUser.UserID, reportUser.SSToken, reportData)
		logx.Infof("RoomID:%v ReportGameSettle reportData:%v, resp:%v, err:%v", slf.RoomID, tools.GetObj(reportData), tools.GetObj(resp), err)
	}(start, end, roundID, endInfoList)
}
