package usermgr

import (
	"fmt"
	"snakes/common/logx"
	"sync"
	"time"
)

type UserManager struct {
	userMap sync.Map // userID -> *User
}

var instance = new(UserManager)

func GetInstance() *UserManager {
	return instance
}

func (u *UserManager) Field(appChanel string, appID int64, id string) string {
	return fmt.Sprintf("%v_%v_%v", appChanel, appID, id)
}

func (u *UserManager) AddUser(user *User) {
	logx.Infof("UserJoin appChannel:%v, appID:%v, userID:%v", user.AppChannel, user.AppID, user.UserID)
	u.userMap.Store(u.Field(user.AppChannel, user.AppID, user.UserID), user)
}

func (u *UserManager) GetUserById(appChannel string, appID int64, userID string) *User {
	value, ok := u.userMap.Load(u.Field(appChannel, appID, userID))
	if ok {
		return value.(*User)
	}
	return nil
}

func (u *UserManager) RmvUser(appChannel string, appID int64, userID string) {
	logx.Infof("RmvUser appChannel:%v, appID:%v, userID:%v", appChannel, appID, userID)
	u.userMap.Delete(u.Field(appChannel, appID, userID))
}

func (u *UserManager) GetOfflineUsers() []*User {
	var nowTime = time.Now().Unix()
	var users []*User
	u.userMap.Range(func(key, value any) bool {
		user := value.(*User)
		if nowTime-user.LastMsgTime < 86400 {
			return true
		}
		users = append(users, user)
		return true
	})
	return users
}
