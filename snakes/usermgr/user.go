package usermgr

import (
	"errors"
	"gorm.io/gorm"
	"ms-version.soofun.online/wjl/game_public/external"
	"ms-version.soofun.online/wjl/game_public/types_public"
	"snakes/common/logx"
	"snakes/common/msg"
	"snakes/common/platform"
	"snakes/common/tools"
	"snakes/common/websocketx"
	"snakes/constvar"
	"snakes/ecode"
	"snakes/model"
	"snakes/model/common/base"
	"snakes/model/common/response"
	"snakes/model/dao"
	"sync"
	"time"
)

type (
	User struct {
		sync.RWMutex
		Socket     *websocketx.WsSocket // 玩家的Socket
		AppID      int64                // appID
		AppChannel string               // 平台渠道
		UserID     string               // 用户ID
		Nickname   string               // 昵称
		Avatar     string               // 头像
		Coin       int64                // 当前金币

		IsVisitor   bool                        // 是否是游客
		IsRobot     bool                        // 是否是机器人
		Code        string                      // h5前端传的code(仅登录获取平台玩家信息有用)
		SSToken     string                      // platform登录成功返回的Token
		GameMode    int                         // 游戏模式 2-半屏 3-全屏
		ClientIP    string                      // 玩家的IP地址
		PlatRoomID  string                      // 客户端传递过来的平台RoomID
		SkinChessID constvar.ProductID          // 棋子的皮肤Id
		AllProduct  map[constvar.ProductID]bool // 所有已购买的商品Id
		Role        constvar.Role               // 玩家角色 0-普通玩家 1-游客 2-管理员
		ConnSrvID   string                      // 连接服务器ID
		LastMsgTime int64                       // 最近消息时间戳
	}
)

func (u *User) SendMessage(msgID string, errCode int, msgObj any) {
	err := dao.GroupDao.GameAckList.Add(u.ConnSrvID, &base.GameAckMsg{
		AppChannel: u.AppChannel,
		AppID:      u.AppID,
		UserID:     u.UserID,
		PlatRoomID: u.PlatRoomID,
		Data: &msg.ToClientMsg{
			MsgID: msgID,
			Code:  errCode,
			Msg:   ecode.GetMsg(errCode),
			Data:  msgObj,
		},
	})
	if err != nil {
		logx.Infof("SendMessage ConnSrvID:%v, err:%v, msgObj:%v", u.ConnSrvID, err, tools.GetObj(msgObj))
	}
}

// ChangeBalance 变更玩家资产
func (u *User) ChangeBalance(actionEvent int, coinChg int64, extend string, gameRoundID string, msgType string, coinType int, coinChgType constvar.CoinChgType, roomType constvar.RoomType) (newCoin int64, errCode int) {
	defer func() {
		logx.Infof("userID:%v ChangeBalance coinChg:%v_%v, newCoin:%v, errCode:%v", u.UserID, coinChg, coinChgType, newCoin, errCode)
		if errCode != ecode.OK {
			logx.Errorf("userID:%v ChangeBalance failed, coinChg:%v_%v, newCoin:%v,errCode:%v, params:%v_%v_%v", u.UserID, coinChg, coinChgType, newCoin, errCode, actionEvent, extend, msgType)
		}
	}()

	var robotPool int64
	if coinChg != 0 {
		if u.IsRobot {
			robotPool, _ = dao.GroupDao.Jackpot.Update(u.AppChannel, u.AppID, coinChg)
		} else {
			var resp *external.ZegoBalanceRes
			var err error
			if coinChg < 0 {
				actionEvent = int(types_public.ActionEventOne)
			}
			switch coinType {
			case 0:
				resp, err = platform.ChangeBalance(u.AppID, u.UserID, u.SSToken, u.AppChannel, actionEvent, int(coinChg), u.PlatRoomID, gameRoundID, extend)
				if err != nil {
					logx.Errorf("userID:%v ChangeBalance err:%v", u.UserID, err)
					return 0, ecode.ErrChangeBalance
				}
			default:
				resp, err = platform.ChangeBalanceByType(u.AppID, u.UserID, u.SSToken, u.AppChannel, actionEvent, int(coinChg), u.PlatRoomID,
					gameRoundID, extend, msgType, coinType)
				if err != nil {
					logx.Errorf("userID:%v ChangeBalanceByType err:%v", u.UserID, err)
					return 0, ecode.ErrChangeBalance
				}
			}

			if resp.Code != 0 {
				if resp.Code >= 1001 && resp.Code <= 1018 {
					return 0, ecode.ErrChangeBalance
				}
				return 0, int(resp.Code)
			}
			u.Coin = int64(resp.NewBalance)
		}
	}

	// 保存金币变化(购买在外部有插入记录)
	if coinChgType != constvar.CoinChangeTypeBuyProduct {
		_ = dao.GroupDao.GameRecordPersist.Add(&model.GameRecord{
			AppChannel:  u.AppChannel,
			AppID:       u.AppID,
			UserID:      u.UserID,
			IsRobot:     u.IsRobot,
			ChangeType:  string(coinChgType),
			ChangeCount: int(coinChg),
			ChangeTime:  time.Now(),
			RoundID:     gameRoundID,
			ResultCoin:  u.Coin,
			RobotPool:   robotPool,
			RoomType:    roomType,
		})
	}
	return u.Coin, ecode.OK
}

// UpdateBalance 更新玩家资产
func (u *User) UpdateBalance() int {
	resp, err := platform.GetBalanceInfo(u.UserID, u.AppID, u.AppChannel, u.SSToken, u.ClientIP)
	if err != nil {
		return ecode.ErrRequestUser
	}
	if resp.Code != 0 {
		if resp.Code >= 1001 && resp.Code <= 1018 {
			return ecode.ErrRequestUser
		}
		return int(resp.Code)
	}

	u.Coin = resp.Data.CurrencyBalance
	channelCfg, _ := dao.GroupDao.ChannelConf.Get(u.AppChannel, u.AppID)
	if channelCfg.CoinType != 0 {
		for _, balanceList := range resp.Data.BalanceList {
			if balanceList.CurrencyType == channelCfg.CoinType {
				u.Coin = int64(balanceList.CurrencyAmount)
			}
		}
	}
	return ecode.OK
}

// NoticeUserCoin 通知玩家金币
func (u *User) NoticeUserCoin() {
	notify := &response.NoticeUserCoin{
		UserID: u.UserID,
		Coin:   u.Coin,
	}
	u.SendMessage(constvar.MsgTypeNoticeUserCoin, ecode.OK, notify)
}

// UpdateSkinPackage 更新玩家皮肤和背包
func (u *User) UpdateSkinPackage() error {
	userStat, err := dao.GroupDao.UserStat.Get(u.AppChannel, u.AppID, u.UserID)
	if err != nil {
		userSkin, errSkin := model.NewUserSkinSearch().SetAppChannel(u.AppChannel).SetAppID(u.AppID).SetUserID(u.UserID).First()
		if userSkin.ChessID > 0 {
			u.SkinChessID = userSkin.ChessID
		} else {
			u.SkinChessID = constvar.ProductIDDefaultChess
		}

		userStat = &model.UserStat{ChessID: int(u.SkinChessID), ProductIDs: []int{}}
		packageList, errPack := model.NewUserPackageSearch().SetAppChannel(u.AppChannel).SetAppID(u.AppID).SetUserID(u.UserID).FindNotTotal()
		for _, v := range packageList {
			u.addProduct(v.ProductID)
			userStat.ProductIDs = append(userStat.ProductIDs, int(v.ProductID))
		}
		logx.Infof("UpdateSkinPackage userID:%v, userStatFromDB:%+v", u.UserID, userStat)

		// 查询没有异常的情况，才能更新redis缓存
		if (errSkin == nil || errors.Is(errSkin, gorm.ErrRecordNotFound)) &&
			(errPack == nil || errors.Is(errPack, gorm.ErrRecordNotFound)) {
			_ = dao.GroupDao.UserStat.Set(u.AppChannel, u.AppID, u.UserID, userStat)
		}
	} else {
		if userStat.ChessID > 0 {
			u.SkinChessID = constvar.ProductID(userStat.ChessID)
		} else {
			u.SkinChessID = constvar.ProductIDDefaultChess
		}
		for _, v := range userStat.ProductIDs {
			u.addProduct(constvar.ProductID(v))
		}
		logx.Infof("UpdateSkinPackage userID:%v, userStatFromRedis:%+v", u.UserID, userStat)
	}
	return nil
}

// IsHaveProduct 判断指定商品是否已经购买
func (u *User) IsHaveProduct(productID constvar.ProductID) bool {
	u.RLock()
	defer u.RUnlock()

	if productID.IsDefault() {
		return true
	}

	_, ok := u.AllProduct[productID]
	return ok
}

// addProduct 添加商品
func (u *User) addProduct(productID constvar.ProductID) {
	u.Lock()
	defer u.Unlock()

	u.AllProduct[productID] = true
}

// getProducts 商品列表
func (u *User) getProducts() []int {
	u.RLock()
	defer u.RUnlock()

	var productIDs = make([]int, 0)
	for v := range u.AllProduct {
		productIDs = append(productIDs, int(v))
	}
	return productIDs
}

// AddProduct 添加商品
func (u *User) AddProduct(productID constvar.ProductID) error {
	u.addProduct(productID)
	var userPackage = &model.UserPackage{
		AppChannel: u.AppChannel,
		AppID:      u.AppID,
		UserID:     u.UserID,
		ProductID:  productID,
		CreateTime: time.Now(),
	}
	_, err := model.NewUserPackageSearch().Create(userPackage)
	if err != nil {
		logx.Errorf("AddProduct Create err:%v, userPackage:%+v", err, userPackage)
	}
	logx.Infof("AddProduct userID:%v, productID:%v", u.UserID, productID)
	_ = dao.GroupDao.UserStat.Del(u.AppChannel, u.AppID, u.UserID)
	_ = u.UpdateSkinPackage()
	return nil
}

// SetSkin 设置皮肤
func (u *User) SetSkin(productID constvar.ProductID) error {
	if u.SkinChessID == productID {
		logx.Infof("SetSkin userID:%v, oldSkinID:%+v, productID:%v equal", u.UserID, u.SkinChessID, productID)
		return nil
	}

	u.SkinChessID = productID
	var userStat = &model.UserStat{
		ChessID:    int(u.SkinChessID),
		ProductIDs: u.getProducts(),
	}
	logx.Infof("SetSkin userID:%v, newUserStat:%+v", u.UserID, userStat)
	_ = dao.GroupDao.UserStat.Set(u.AppChannel, u.AppID, u.UserID, userStat)
	_ = dao.GroupDao.UserSkinPersist.Add(&model.UserSkin{
		AppChannel: u.AppChannel,
		AppID:      u.AppID,
		UserID:     u.UserID,
		ChessID:    productID,
		CreateTime: time.Now(),
	})
	return nil
}

// ChangeRole 改变玩家角色
func (u *User) ChangeRole(newRole constvar.Role) {
	logx.Infof("ChangeRole userID:%v, oldRole:%v, newRole:%v", u.UserID, u.Role, newRole)
	u.Role = newRole
}
